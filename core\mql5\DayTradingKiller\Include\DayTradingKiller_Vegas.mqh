//+------------------------------------------------------------------+
//|                                DayTradingKiller_Vegas.mqh       |
//|                                    日内交易大杀器 - Vegas系统类   |
//|                                    Copyright 2025, Your Company |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Company"
#property link      "https://www.mql5.com"

//--- Vegas信号强度枚举
enum ENUM_VEGAS_SIGNAL
{
    VEGAS_SIGNAL_NONE,      // 无信号
    VEGAS_SIGNAL_WEAK,      // 弱信号
    VEGAS_SIGNAL_MEDIUM,    // 中等信号
    VEGAS_SIGNAL_STRONG,    // 强信号
    VEGAS_SIGNAL_VERY_STRONG // 极强信号
};

//--- 市场阶段枚举
enum ENUM_MARKET_PHASE
{
    PHASE_ACCUMULATION,     // 积累阶段
    PHASE_MARKUP,          // 上涨阶段
    PHASE_DISTRIBUTION,    // 分配阶段
    PHASE_MARKDOWN,        // 下跌阶段
    PHASE_CONSOLIDATION    // 整理阶段
};

//--- Vegas分析结构
struct SVegasAnalysis
{
    string trend_direction;
    ENUM_VEGAS_SIGNAL signal_strength;
    string signal_strength_text;
    bool is_trending;
    double ema_alignment_score;
    ENUM_MARKET_PHASE market_phase;
    string market_phase_text;
    color trend_color;
    bool tunnel_aligned;
    double tunnel_width;
    string recommendation;
};

//+------------------------------------------------------------------+
//| Vegas隧道系统类                                                   |
//+------------------------------------------------------------------+
class CDayTradingVegas
{
private:
    // EMA指标句柄
    int m_ema12_handle;
    int m_ema21_handle;
    int m_ema34_handle;
    int m_ema55_handle;
    int m_ema89_handle;
    int m_ema144_handle;
    int m_ema233_handle;
    int m_ema377_handle;
    
    // Vegas参数
    int m_ema_periods[8];
    
    // 分析结果缓存
    SVegasAnalysis m_current_analysis;
    datetime m_last_update_time;
    
public:
    CDayTradingVegas();
    ~CDayTradingVegas();
    
    bool Initialize();
    void UpdateVegasAnalysis();
    void GetCurrentAnalysis(SVegasAnalysis &analysis);
    bool IsTrendingMarket();
    string GetTradingRecommendation();
    
private:
    bool CreateEMAHandles();
    void ReleaseEMAHandles();
    double GetEMAValue(int handle, int shift = 0);
    string AnalyzeTrendDirection();
    ENUM_VEGAS_SIGNAL CalculateSignalStrength();
    string GetSignalStrengthText(ENUM_VEGAS_SIGNAL signal);
    double CalculateEMAAlignment();
    ENUM_MARKET_PHASE DetermineMarketPhase();
    string GetMarketPhaseText(ENUM_MARKET_PHASE phase);
    color GetTrendColor(string direction, ENUM_VEGAS_SIGNAL strength);
    bool CheckTunnelAlignment();
    double CalculateTunnelWidth();
    string GenerateRecommendation();
    bool IsEMASequenceValid(bool bullish);
};

//+------------------------------------------------------------------+
//| 构造函数                                                          |
//+------------------------------------------------------------------+
CDayTradingVegas::CDayTradingVegas()
{
    // 初始化EMA周期
    m_ema_periods[0] = 12;
    m_ema_periods[1] = 21;
    m_ema_periods[2] = 34;
    m_ema_periods[3] = 55;
    m_ema_periods[4] = 89;
    m_ema_periods[5] = 144;
    m_ema_periods[6] = 233;
    m_ema_periods[7] = 377;
    
    // 初始化句柄
    m_ema12_handle = INVALID_HANDLE;
    m_ema21_handle = INVALID_HANDLE;
    m_ema34_handle = INVALID_HANDLE;
    m_ema55_handle = INVALID_HANDLE;
    m_ema89_handle = INVALID_HANDLE;
    m_ema144_handle = INVALID_HANDLE;
    m_ema233_handle = INVALID_HANDLE;
    m_ema377_handle = INVALID_HANDLE;
    
    m_last_update_time = 0;
}

//+------------------------------------------------------------------+
//| 析构函数                                                          |
//+------------------------------------------------------------------+
CDayTradingVegas::~CDayTradingVegas()
{
    ReleaseEMAHandles();
}

//+------------------------------------------------------------------+
//| 初始化                                                            |
//+------------------------------------------------------------------+
bool CDayTradingVegas::Initialize()
{
    if(!CreateEMAHandles())
    {
        Print("Vegas系统初始化失败：无法创建EMA指标");
        return false;
    }
    
    Print("Vegas隧道系统初始化成功");
    return true;
}

//+------------------------------------------------------------------+
//| 更新Vegas分析                                                     |
//+------------------------------------------------------------------+
void CDayTradingVegas::UpdateVegasAnalysis()
{
    // 分析趋势方向
    m_current_analysis.trend_direction = AnalyzeTrendDirection();
    
    // 计算信号强度
    m_current_analysis.signal_strength = CalculateSignalStrength();
    m_current_analysis.signal_strength_text = GetSignalStrengthText(m_current_analysis.signal_strength);
    
    // 判断是否趋势市场
    m_current_analysis.is_trending = IsTrendingMarket();
    
    // 计算EMA排列得分
    m_current_analysis.ema_alignment_score = CalculateEMAAlignment();
    
    // 确定市场阶段
    m_current_analysis.market_phase = DetermineMarketPhase();
    m_current_analysis.market_phase_text = GetMarketPhaseText(m_current_analysis.market_phase);
    
    // 设置趋势颜色
    m_current_analysis.trend_color = GetTrendColor(m_current_analysis.trend_direction, m_current_analysis.signal_strength);
    
    // 检查隧道排列
    m_current_analysis.tunnel_aligned = CheckTunnelAlignment();
    
    // 计算隧道宽度
    m_current_analysis.tunnel_width = CalculateTunnelWidth();
    
    // 生成交易建议
    m_current_analysis.recommendation = GenerateRecommendation();
    
    m_last_update_time = TimeCurrent();
}

//+------------------------------------------------------------------+
//| 获取当前分析结果                                                  |
//+------------------------------------------------------------------+
void CDayTradingVegas::GetCurrentAnalysis(SVegasAnalysis &analysis)
{
    analysis = m_current_analysis;
}

//+------------------------------------------------------------------+
//| 判断是否趋势市场                                                  |
//+------------------------------------------------------------------+
bool CDayTradingVegas::IsTrendingMarket()
{
    return m_current_analysis.ema_alignment_score > 70.0 && m_current_analysis.tunnel_aligned;
}

//+------------------------------------------------------------------+
//| 获取交易建议                                                      |
//+------------------------------------------------------------------+
string CDayTradingVegas::GetTradingRecommendation()
{
    return m_current_analysis.recommendation;
}

//+------------------------------------------------------------------+
//| 创建EMA指标句柄                                                   |
//+------------------------------------------------------------------+
bool CDayTradingVegas::CreateEMAHandles()
{
    m_ema12_handle = iMA(Symbol(), PERIOD_CURRENT, m_ema_periods[0], 0, MODE_EMA, PRICE_CLOSE);
    m_ema21_handle = iMA(Symbol(), PERIOD_CURRENT, m_ema_periods[1], 0, MODE_EMA, PRICE_CLOSE);
    m_ema34_handle = iMA(Symbol(), PERIOD_CURRENT, m_ema_periods[2], 0, MODE_EMA, PRICE_CLOSE);
    m_ema55_handle = iMA(Symbol(), PERIOD_CURRENT, m_ema_periods[3], 0, MODE_EMA, PRICE_CLOSE);
    m_ema89_handle = iMA(Symbol(), PERIOD_CURRENT, m_ema_periods[4], 0, MODE_EMA, PRICE_CLOSE);
    m_ema144_handle = iMA(Symbol(), PERIOD_CURRENT, m_ema_periods[5], 0, MODE_EMA, PRICE_CLOSE);
    m_ema233_handle = iMA(Symbol(), PERIOD_CURRENT, m_ema_periods[6], 0, MODE_EMA, PRICE_CLOSE);
    m_ema377_handle = iMA(Symbol(), PERIOD_CURRENT, m_ema_periods[7], 0, MODE_EMA, PRICE_CLOSE);
    
    return (m_ema12_handle != INVALID_HANDLE && m_ema21_handle != INVALID_HANDLE &&
            m_ema34_handle != INVALID_HANDLE && m_ema55_handle != INVALID_HANDLE &&
            m_ema89_handle != INVALID_HANDLE && m_ema144_handle != INVALID_HANDLE &&
            m_ema233_handle != INVALID_HANDLE && m_ema377_handle != INVALID_HANDLE);
}

//+------------------------------------------------------------------+
//| 释放EMA指标句柄                                                   |
//+------------------------------------------------------------------+
void CDayTradingVegas::ReleaseEMAHandles()
{
    if(m_ema12_handle != INVALID_HANDLE) IndicatorRelease(m_ema12_handle);
    if(m_ema21_handle != INVALID_HANDLE) IndicatorRelease(m_ema21_handle);
    if(m_ema34_handle != INVALID_HANDLE) IndicatorRelease(m_ema34_handle);
    if(m_ema55_handle != INVALID_HANDLE) IndicatorRelease(m_ema55_handle);
    if(m_ema89_handle != INVALID_HANDLE) IndicatorRelease(m_ema89_handle);
    if(m_ema144_handle != INVALID_HANDLE) IndicatorRelease(m_ema144_handle);
    if(m_ema233_handle != INVALID_HANDLE) IndicatorRelease(m_ema233_handle);
    if(m_ema377_handle != INVALID_HANDLE) IndicatorRelease(m_ema377_handle);
}

//+------------------------------------------------------------------+
//| 获取EMA值                                                         |
//+------------------------------------------------------------------+
double CDayTradingVegas::GetEMAValue(int handle, int shift = 0)
{
    if(handle == INVALID_HANDLE)
        return 0;
    
    double buffer[1];
    if(CopyBuffer(handle, 0, shift, 1, buffer) <= 0)
        return 0;
    
    return buffer[0];
}

//+------------------------------------------------------------------+
//| 分析趋势方向                                                      |
//+------------------------------------------------------------------+
string CDayTradingVegas::AnalyzeTrendDirection()
{
    double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double ema12 = GetEMAValue(m_ema12_handle);
    double ema21 = GetEMAValue(m_ema21_handle);
    double ema55 = GetEMAValue(m_ema55_handle);
    double ema144 = GetEMAValue(m_ema144_handle);
    
    // 检查多头排列
    bool bullish_alignment = IsEMASequenceValid(true);
    // 检查空头排列
    bool bearish_alignment = IsEMASequenceValid(false);
    
    if(bullish_alignment && current_price > ema12)
        return "强势上涨";
    else if(bearish_alignment && current_price < ema12)
        return "强势下跌";
    else if(current_price > ema55)
        return "偏多";
    else if(current_price < ema55)
        return "偏空";
    else
        return "震荡";
}

//+------------------------------------------------------------------+
//| 计算信号强度                                                      |
//+------------------------------------------------------------------+
ENUM_VEGAS_SIGNAL CDayTradingVegas::CalculateSignalStrength()
{
    double alignment_score = CalculateEMAAlignment();
    bool tunnel_aligned = CheckTunnelAlignment();
    
    if(alignment_score >= 90 && tunnel_aligned)
        return VEGAS_SIGNAL_VERY_STRONG;
    else if(alignment_score >= 75 && tunnel_aligned)
        return VEGAS_SIGNAL_STRONG;
    else if(alignment_score >= 60)
        return VEGAS_SIGNAL_MEDIUM;
    else if(alignment_score >= 40)
        return VEGAS_SIGNAL_WEAK;
    else
        return VEGAS_SIGNAL_NONE;
}

//+------------------------------------------------------------------+
//| 获取信号强度文本                                                  |
//+------------------------------------------------------------------+
string CDayTradingVegas::GetSignalStrengthText(ENUM_VEGAS_SIGNAL signal)
{
    switch(signal)
    {
        case VEGAS_SIGNAL_VERY_STRONG:
            return "极强";
        case VEGAS_SIGNAL_STRONG:
            return "强";
        case VEGAS_SIGNAL_MEDIUM:
            return "中等";
        case VEGAS_SIGNAL_WEAK:
            return "弱";
        case VEGAS_SIGNAL_NONE:
        default:
            return "无";
    }
}

//+------------------------------------------------------------------+
//| 计算EMA排列得分                                                   |
//+------------------------------------------------------------------+
double CDayTradingVegas::CalculateEMAAlignment()
{
    double ema_values[8];
    int handles[8] = {m_ema12_handle, m_ema21_handle, m_ema34_handle, m_ema55_handle,
                      m_ema89_handle, m_ema144_handle, m_ema233_handle, m_ema377_handle};
    
    // 获取所有EMA值
    for(int i = 0; i < 8; i++)
    {
        ema_values[i] = GetEMAValue(handles[i]);
        if(ema_values[i] <= 0)
            return 0;
    }
    
    // 检查多头排列得分
    int bullish_score = 0;
    for(int i = 0; i < 7; i++)
    {
        if(ema_values[i] > ema_values[i + 1])
            bullish_score++;
    }
    
    // 检查空头排列得分
    int bearish_score = 0;
    for(int i = 0; i < 7; i++)
    {
        if(ema_values[i] < ema_values[i + 1])
            bearish_score++;
    }
    
    // 返回最高得分的百分比
    int max_score = MathMax(bullish_score, bearish_score);
    return (double)max_score / 7.0 * 100.0;
}

//+------------------------------------------------------------------+
//| 确定市场阶段                                                      |
//+------------------------------------------------------------------+
ENUM_MARKET_PHASE CDayTradingVegas::DetermineMarketPhase()
{
    double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double ema12 = GetEMAValue(m_ema12_handle);
    double ema21 = GetEMAValue(m_ema21_handle);
    double ema55 = GetEMAValue(m_ema55_handle);
    double ema144 = GetEMAValue(m_ema144_handle);
    
    // 获取前一根K线的EMA值进行比较
    double prev_ema12 = GetEMAValue(m_ema12_handle, 1);
    double prev_ema21 = GetEMAValue(m_ema21_handle, 1);
    
    bool emas_rising = (ema12 > prev_ema12 && ema21 > prev_ema21);
    bool emas_falling = (ema12 < prev_ema12 && ema21 < prev_ema21);
    bool price_above_emas = (current_price > ema12 && current_price > ema21);
    bool price_below_emas = (current_price < ema12 && current_price < ema21);
    
    if(emas_rising && price_above_emas && IsEMASequenceValid(true))
        return PHASE_MARKUP;
    else if(emas_falling && price_below_emas && IsEMASequenceValid(false))
        return PHASE_MARKDOWN;
    else if(current_price > ema144 && !emas_rising)
        return PHASE_DISTRIBUTION;
    else if(current_price < ema144 && !emas_falling)
        return PHASE_ACCUMULATION;
    else
        return PHASE_CONSOLIDATION;
}

//+------------------------------------------------------------------+
//| 获取市场阶段文本                                                  |
//+------------------------------------------------------------------+
string CDayTradingVegas::GetMarketPhaseText(ENUM_MARKET_PHASE phase)
{
    switch(phase)
    {
        case PHASE_ACCUMULATION:
            return "积累阶段";
        case PHASE_MARKUP:
            return "上涨阶段";
        case PHASE_DISTRIBUTION:
            return "分配阶段";
        case PHASE_MARKDOWN:
            return "下跌阶段";
        case PHASE_CONSOLIDATION:
            return "整理阶段";
        default:
            return "未知阶段";
    }
}

//+------------------------------------------------------------------+
//| 获取趋势颜色                                                      |
//+------------------------------------------------------------------+
color CDayTradingVegas::GetTrendColor(string direction, ENUM_VEGAS_SIGNAL strength)
{
    if(direction == "强势上涨" || direction == "偏多")
    {
        switch(strength)
        {
            case VEGAS_SIGNAL_VERY_STRONG:
                return clrLimeGreen;
            case VEGAS_SIGNAL_STRONG:
                return clrGreen;
            case VEGAS_SIGNAL_MEDIUM:
                return clrDarkGreen;
            default:
                return clrLightGreen;
        }
    }
    else if(direction == "强势下跌" || direction == "偏空")
    {
        switch(strength)
        {
            case VEGAS_SIGNAL_VERY_STRONG:
                return clrRed;
            case VEGAS_SIGNAL_STRONG:
                return clrCrimson;
            case VEGAS_SIGNAL_MEDIUM:
                return clrDarkRed;
            default:
                return clrLightCoral;
        }
    }
    else
    {
        return clrGray;
    }
}

//+------------------------------------------------------------------+
//| 检查隧道排列                                                      |
//+------------------------------------------------------------------+
bool CDayTradingVegas::CheckTunnelAlignment()
{
    // 检查短期EMA组 (12, 21, 34, 55)
    double short_emas[4];
    int short_handles[4] = {m_ema12_handle, m_ema21_handle, m_ema34_handle, m_ema55_handle};
    
    for(int i = 0; i < 4; i++)
    {
        short_emas[i] = GetEMAValue(short_handles[i]);
        if(short_emas[i] <= 0)
            return false;
    }
    
    // 检查多头排列
    bool bullish_aligned = true;
    for(int i = 0; i < 3; i++)
    {
        if(short_emas[i] <= short_emas[i + 1])
        {
            bullish_aligned = false;
            break;
        }
    }
    
    // 检查空头排列
    bool bearish_aligned = true;
    for(int i = 0; i < 3; i++)
    {
        if(short_emas[i] >= short_emas[i + 1])
        {
            bearish_aligned = false;
            break;
        }
    }
    
    return bullish_aligned || bearish_aligned;
}

//+------------------------------------------------------------------+
//| 计算隧道宽度                                                      |
//+------------------------------------------------------------------+
double CDayTradingVegas::CalculateTunnelWidth()
{
    double ema12 = GetEMAValue(m_ema12_handle);
    double ema55 = GetEMAValue(m_ema55_handle);
    
    if(ema12 <= 0 || ema55 <= 0)
        return 0;
    
    return MathAbs(ema12 - ema55);
}

//+------------------------------------------------------------------+
//| 生成交易建议                                                      |
//+------------------------------------------------------------------+
string CDayTradingVegas::GenerateRecommendation()
{
    string direction = m_current_analysis.trend_direction;
    ENUM_VEGAS_SIGNAL strength = m_current_analysis.signal_strength;
    ENUM_MARKET_PHASE phase = m_current_analysis.market_phase;
    bool tunnel_aligned = m_current_analysis.tunnel_aligned;
    
    // 强信号建议
    if(strength >= VEGAS_SIGNAL_STRONG && tunnel_aligned)
    {
        if(direction == "强势上涨")
            return "🚀 强烈建议做多！趋势明确！";
        else if(direction == "强势下跌")
            return "📉 强烈建议做空！趋势明确！";
    }
    
    // 中等信号建议
    if(strength == VEGAS_SIGNAL_MEDIUM)
    {
        if(direction == "偏多")
            return "📈 可考虑轻仓做多";
        else if(direction == "偏空")
            return "📉 可考虑轻仓做空";
    }
    
    // 根据市场阶段给建议
    switch(phase)
    {
        case PHASE_ACCUMULATION:
            return "💰 积累阶段，寻找做多机会";
        case PHASE_MARKUP:
            return "🚀 上涨阶段，顺势做多";
        case PHASE_DISTRIBUTION:
            return "⚠️ 分配阶段，准备做空";
        case PHASE_MARKDOWN:
            return "📉 下跌阶段，顺势做空";
        case PHASE_CONSOLIDATION:
            return "😴 整理阶段，等待突破";
    }
    
    // 默认建议
    if(strength <= VEGAS_SIGNAL_WEAK)
        return "⏳ 信号不明确，观望为主";
    
    return "📊 继续观察市场动向";
}

//+------------------------------------------------------------------+
//| 检查EMA序列有效性                                                 |
//+------------------------------------------------------------------+
bool CDayTradingVegas::IsEMASequenceValid(bool bullish)
{
    double ema_values[8];
    int handles[8] = {m_ema12_handle, m_ema21_handle, m_ema34_handle, m_ema55_handle,
                      m_ema89_handle, m_ema144_handle, m_ema233_handle, m_ema377_handle};
    
    // 获取所有EMA值
    for(int i = 0; i < 8; i++)
    {
        ema_values[i] = GetEMAValue(handles[i]);
        if(ema_values[i] <= 0)
            return false;
    }
    
    int valid_count = 0;
    
    if(bullish)
    {
        // 检查多头排列：短期EMA > 长期EMA
        for(int i = 0; i < 7; i++)
        {
            if(ema_values[i] > ema_values[i + 1])
                valid_count++;
        }
    }
    else
    {
        // 检查空头排列：短期EMA < 长期EMA
        for(int i = 0; i < 7; i++)
        {
            if(ema_values[i] < ema_values[i + 1])
                valid_count++;
        }
    }
    
    // 至少5个EMA符合排列才认为有效
    return valid_count >= 5;
}
