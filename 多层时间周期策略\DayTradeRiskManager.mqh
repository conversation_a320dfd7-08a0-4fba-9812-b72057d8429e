//+------------------------------------------------------------------+
//|                                        DayTradeRiskManager.mqh |
//|                                  Copyright 2024, TradingSystem |
//|                                    日内交易风险管理模块         |
//+------------------------------------------------------------------+

#ifndef DAY_TRADE_RISK_MANAGER_H
#define DAY_TRADE_RISK_MANAGER_H

//+------------------------------------------------------------------+
//| 日内交易风险管理类                                               |
//+------------------------------------------------------------------+
class CDayTradeRiskManager
{
private:
    // 风险参数
    double m_riskPercent;               // 单笔风险百分比
    double m_stopLossATR;               // 止损ATR倍数
    double m_takeProfitRatio;           // 止盈风险比
    
    // ATR指标句柄
    int m_atrHandle;                    // ATR指标句柄
    
    // 内部方法
    double GetATRValue(string symbol, ENUM_TIMEFRAMES tf);
    double GetAccountRiskAmount();
    double CalculateStopLossDistance(string symbol, ENUM_SIGNAL_TYPE signal);

public:
    // 构造函数和析构函数
    CDayTradeRiskManager();
    ~CDayTradeRiskManager();
    
    // 初始化方法
    bool Initialize(double riskPercent, double stopLossATR, double takeProfitRatio);
    
    // 主要风险管理方法
    double CalculatePositionSize(string symbol);
    double CalculateStopLoss(string symbol, ENUM_SIGNAL_TYPE signal);
    double CalculateTakeProfit(string symbol, ENUM_SIGNAL_TYPE signal, double stopLoss);
    
    // 风险检查方法
    bool IsRiskAcceptable(string symbol, double lotSize, double stopLoss);
    bool IsPositionSizeValid(string symbol, double lotSize);
    
    // 获取风险信息
    double GetMaxRiskAmount() { return GetAccountRiskAmount(); }
    double GetCurrentRiskPercent() { return m_riskPercent; }
    
    // 调试方法
    void PrintRiskCalculation(string symbol, double lotSize, double stopLoss, double takeProfit);
};

//+------------------------------------------------------------------+
//| 构造函数                                                         |
//+------------------------------------------------------------------+
CDayTradeRiskManager::CDayTradeRiskManager()
{
    m_riskPercent = 1.0;
    m_stopLossATR = 2.0;
    m_takeProfitRatio = 2.0;
    m_atrHandle = INVALID_HANDLE;
}

//+------------------------------------------------------------------+
//| 析构函数                                                         |
//+------------------------------------------------------------------+
CDayTradeRiskManager::~CDayTradeRiskManager()
{
    if(m_atrHandle != INVALID_HANDLE)
        IndicatorRelease(m_atrHandle);
}

//+------------------------------------------------------------------+
//| 初始化方法                                                       |
//+------------------------------------------------------------------+
bool CDayTradeRiskManager::Initialize(double riskPercent, double stopLossATR, double takeProfitRatio)
{
    m_riskPercent = riskPercent;
    m_stopLossATR = stopLossATR;
    m_takeProfitRatio = takeProfitRatio;
    
    Print("日内交易风险管理器初始化成功");
    Print("风险百分比: ", m_riskPercent, "%");
    Print("止损ATR倍数: ", m_stopLossATR);
    Print("止盈风险比: ", m_takeProfitRatio);
    
    return true;
}

//+------------------------------------------------------------------+
//| 计算仓位大小                                                     |
//+------------------------------------------------------------------+
double CDayTradeRiskManager::CalculatePositionSize(string symbol)
{
    // 获取账户风险金额
    double riskAmount = GetAccountRiskAmount();
    if(riskAmount <= 0)
    {
        Print("风险金额计算错误");
        return 0;
    }
    
    // 计算止损距离
    double stopLossDistance = CalculateStopLossDistance(symbol, SIGNAL_BUY);
    if(stopLossDistance <= 0)
    {
        Print("止损距离计算错误");
        return 0;
    }
    
    // 获取品种信息
    double tickSize = SymbolInfoDouble(symbol, SYMBOL_TRADE_TICK_SIZE);
    double tickValue = SymbolInfoDouble(symbol, SYMBOL_TRADE_TICK_VALUE);
    double minLot = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MIN);
    double maxLot = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MAX);
    double lotStep = SymbolInfoDouble(symbol, SYMBOL_VOLUME_STEP);
    
    if(tickSize <= 0 || tickValue <= 0)
    {
        Print("品种信息获取失败");
        return 0;
    }
    
    // 计算每点价值
    double pointValue = tickValue;
    if(tickSize != SymbolInfoDouble(symbol, SYMBOL_POINT))
    {
        pointValue = tickValue * SymbolInfoDouble(symbol, SYMBOL_POINT) / tickSize;
    }
    
    // 计算仓位大小
    double lotSize = riskAmount / (stopLossDistance * pointValue);
    
    // 调整到有效的手数
    lotSize = MathFloor(lotSize / lotStep) * lotStep;
    lotSize = MathMax(minLot, MathMin(maxLot, lotSize));
    
    Print("仓位计算: 风险金额=", riskAmount, " 止损距离=", stopLossDistance, " 计算手数=", lotSize);
    
    return lotSize;
}

//+------------------------------------------------------------------+
//| 计算止损价格                                                     |
//+------------------------------------------------------------------+
double CDayTradeRiskManager::CalculateStopLoss(string symbol, ENUM_SIGNAL_TYPE signal)
{
    double currentPrice = SymbolInfoDouble(symbol, SYMBOL_BID);
    if(currentPrice <= 0) return 0;
    
    double stopLossDistance = CalculateStopLossDistance(symbol, signal);
    if(stopLossDistance <= 0) return 0;
    
    double stopLoss = 0;
    
    if(signal == SIGNAL_BUY)
    {
        stopLoss = currentPrice - stopLossDistance;
    }
    else if(signal == SIGNAL_SELL)
    {
        stopLoss = currentPrice + stopLossDistance;
    }
    
    // 确保止损价格有效
    double minStopLevel = SymbolInfoInteger(symbol, SYMBOL_TRADE_STOPS_LEVEL) * SymbolInfoDouble(symbol, SYMBOL_POINT);
    
    if(signal == SIGNAL_BUY)
    {
        stopLoss = MathMin(stopLoss, currentPrice - minStopLevel);
    }
    else if(signal == SIGNAL_SELL)
    {
        stopLoss = MathMax(stopLoss, currentPrice + minStopLevel);
    }
    
    return stopLoss;
}

//+------------------------------------------------------------------+
//| 计算止盈价格                                                     |
//+------------------------------------------------------------------+
double CDayTradeRiskManager::CalculateTakeProfit(string symbol, ENUM_SIGNAL_TYPE signal, double stopLoss)
{
    double currentPrice = SymbolInfoDouble(symbol, SYMBOL_ASK);
    if(currentPrice <= 0 || stopLoss <= 0) return 0;
    
    double stopLossDistance = MathAbs(currentPrice - stopLoss);
    double takeProfitDistance = stopLossDistance * m_takeProfitRatio;
    
    double takeProfit = 0;
    
    if(signal == SIGNAL_BUY)
    {
        takeProfit = currentPrice + takeProfitDistance;
    }
    else if(signal == SIGNAL_SELL)
    {
        takeProfit = currentPrice - takeProfitDistance;
    }
    
    // 确保止盈价格有效
    double minStopLevel = SymbolInfoInteger(symbol, SYMBOL_TRADE_STOPS_LEVEL) * SymbolInfoDouble(symbol, SYMBOL_POINT);
    
    if(signal == SIGNAL_BUY)
    {
        takeProfit = MathMax(takeProfit, currentPrice + minStopLevel);
    }
    else if(signal == SIGNAL_SELL)
    {
        takeProfit = MathMin(takeProfit, currentPrice - minStopLevel);
    }
    
    return takeProfit;
}

//+------------------------------------------------------------------+
//| 获取ATR值                                                        |
//+------------------------------------------------------------------+
double CDayTradeRiskManager::GetATRValue(string symbol, ENUM_TIMEFRAMES tf)
{
    // 创建ATR句柄 (如果还没有)
    if(m_atrHandle == INVALID_HANDLE)
    {
        m_atrHandle = iATR(symbol, tf, 14);
        if(m_atrHandle == INVALID_HANDLE)
        {
            Print("创建ATR指标失败");
            return 0;
        }
    }
    
    double buffer[1];
    if(CopyBuffer(m_atrHandle, 0, 0, 1, buffer) <= 0)
    {
        Print("获取ATR数据失败");
        return 0;
    }
    
    return buffer[0];
}

//+------------------------------------------------------------------+
//| 获取账户风险金额                                                 |
//+------------------------------------------------------------------+
double CDayTradeRiskManager::GetAccountRiskAmount()
{
    double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    if(accountBalance <= 0)
    {
        accountBalance = AccountInfoDouble(ACCOUNT_EQUITY);
    }
    
    return accountBalance * m_riskPercent / 100.0;
}

//+------------------------------------------------------------------+
//| 计算止损距离                                                     |
//+------------------------------------------------------------------+
double CDayTradeRiskManager::CalculateStopLossDistance(string symbol, ENUM_SIGNAL_TYPE signal)
{
    // 使用5分钟图的ATR来计算止损距离 (更适合日内交易)
    double atrValue = GetATRValue(symbol, PERIOD_M5);
    if(atrValue <= 0)
    {
        Print("ATR值获取失败，使用默认止损距离");
        // 使用默认止损距离 (例如50点)
        return 50 * SymbolInfoDouble(symbol, SYMBOL_POINT);
    }
    
    return atrValue * m_stopLossATR;
}

//+------------------------------------------------------------------+
//| 检查风险是否可接受                                               |
//+------------------------------------------------------------------+
bool CDayTradeRiskManager::IsRiskAcceptable(string symbol, double lotSize, double stopLoss)
{
    double currentPrice = SymbolInfoDouble(symbol, SYMBOL_BID);
    if(currentPrice <= 0 || stopLoss <= 0) return false;
    
    double stopLossDistance = MathAbs(currentPrice - stopLoss);
    double tickValue = SymbolInfoDouble(symbol, SYMBOL_TRADE_TICK_VALUE);
    double point = SymbolInfoDouble(symbol, SYMBOL_POINT);
    
    // 计算潜在损失
    double potentialLoss = lotSize * stopLossDistance * tickValue / point;
    double maxRiskAmount = GetAccountRiskAmount();
    
    return (potentialLoss <= maxRiskAmount * 1.1); // 允许10%的缓冲
}

//+------------------------------------------------------------------+
//| 检查仓位大小是否有效                                             |
//+------------------------------------------------------------------+
bool CDayTradeRiskManager::IsPositionSizeValid(string symbol, double lotSize)
{
    double minLot = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MIN);
    double maxLot = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MAX);
    double lotStep = SymbolInfoDouble(symbol, SYMBOL_VOLUME_STEP);
    
    // 检查是否在允许范围内
    if(lotSize < minLot || lotSize > maxLot)
        return false;
    
    // 检查是否符合步长要求
    double remainder = fmod(lotSize - minLot, lotStep);
    return (MathAbs(remainder) < 0.0000001);
}

//+------------------------------------------------------------------+
//| 打印风险计算信息                                                 |
//+------------------------------------------------------------------+
void CDayTradeRiskManager::PrintRiskCalculation(string symbol, double lotSize, double stopLoss, double takeProfit)
{
    double currentPrice = SymbolInfoDouble(symbol, SYMBOL_BID);
    double riskAmount = GetAccountRiskAmount();
    double stopLossDistance = MathAbs(currentPrice - stopLoss);
    double takeProfitDistance = MathAbs(takeProfit - currentPrice);
    
    Print("=== 风险计算详情 ===");
    Print("品种: ", symbol);
    Print("当前价格: ", currentPrice);
    Print("仓位大小: ", lotSize);
    Print("止损价格: ", stopLoss, " (距离: ", stopLossDistance, ")");
    Print("止盈价格: ", takeProfit, " (距离: ", takeProfitDistance, ")");
    Print("风险金额: ", riskAmount);
    Print("风险回报比: 1:", takeProfitDistance/stopLossDistance);
    Print("==================");
}

#endif // DAY_TRADE_RISK_MANAGER_H
