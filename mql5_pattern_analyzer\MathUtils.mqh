//+------------------------------------------------------------------+
//|                                                     MathUtils.mqh |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"

//+------------------------------------------------------------------+
//| 数学工具类 - 替代Python的科学计算库                                    |
//+------------------------------------------------------------------+
class CMathUtils
{
public:
   // 线性回归（替代TheilSenRegressor）
   static double     CalculateTheilSenRegression(const double &x[], const double &y[], int size, double &slope, double &intercept, double &r_squared);
   static double     CalculateLinearRegression(const double &x[], const double &y[], int size, double &slope, double &intercept, double &r_squared);
   
   // 统计分析
   static double     CalculateMean(const double &data[], int size);
   static double     CalculateStandardDeviation(const double &data[], int size);
   static double     CalculateMedian(const double &data[], int size);
   static double     CalculatePercentile(const double &data[], int size, double percentile);
   
   // 核密度估计（替代scipy KDE）
   static double     CalculateKDE(const double &data[], int size, double point, double bandwidth = 0);
   static void       CalculateKDEArray(const double &data[], int size, const double &points[], int point_count, double &density[], double bandwidth = 0);
   
   // 局部极值检测（替代scipy.signal.argrelextrema）
   static void       FindLocalExtrema(const double &data[], int size, int window, int &peaks[], int &peak_count, int &troughs[], int &trough_count);
   static void       FindSignificantExtrema(const double &data[], int size, double threshold, int &peaks[], int &peak_count, int &troughs[], int &trough_count);
   
   // 斐波那契计算
   static void       CalculateFibonacciLevels(double high, double low, double &fib_levels[]);
   static bool       IsAtFibonacciLevel(double price, double high, double low, double tolerance = 0.01);
   
   // 趋势分析
   static double     CalculateTrendStrength(const double &data[], int size);
   static bool       IsUptrend(const double &data[], int size, int period = 10);
   static bool       IsDowntrend(const double &data[], int size, int period = 10);
   
   // 价格分析
   static void       CalculatePriceDistribution(const double &data[], int size, double &support_levels[], double &resistance_levels[], int &level_count);
   static double     CalculateVolatility(const double &data[], int size, int period = 20);
   
private:
   static double     GaussianKernel(double u);
   static void       QuickSort(double &arr[], int left, int right);
   static int        Partition(double &arr[], int left, int right);
};

//+------------------------------------------------------------------+
//| Theil-Sen回归实现（抗异常值）                                         |
//+------------------------------------------------------------------+
double CMathUtils::CalculateTheilSenRegression(const double &x[], const double &y[], int size, double &slope, double &intercept, double &r_squared)
{
   if(size < 2) { slope = 0; intercept = 0; r_squared = 0; return 0; }
   
   // 计算所有点对的斜率
   double slopes[];
   int slope_count = 0;
   int max_pairs = (size * (size - 1)) / 2;
   ArrayResize(slopes, max_pairs);
   
   for(int i = 0; i < size - 1; i++)
   {
      for(int j = i + 1; j < size; j++)
      {
         if(MathAbs(x[j] - x[i]) > 1e-10)
         {
            slopes[slope_count] = (y[j] - y[i]) / (x[j] - x[i]);
            slope_count++;
         }
      }
   }
   
   if(slope_count == 0) { slope = 0; intercept = CalculateMean(y, size); r_squared = 0; return intercept; }
   
   // 取中位数作为斜率
   slope = CalculateMedian(slopes, slope_count);
   
   // 计算截距的中位数
   double intercepts[];
   ArrayResize(intercepts, size);
   for(int i = 0; i < size; i++)
   {
      intercepts[i] = y[i] - slope * x[i];
   }
   intercept = CalculateMedian(intercepts, size);
   
   // 计算R²
   double ss_tot = 0, ss_res = 0;
   double y_mean = CalculateMean(y, size);
   
   for(int i = 0; i < size; i++)
   {
      double predicted = slope * x[i] + intercept;
      ss_res += (y[i] - predicted) * (y[i] - predicted);
      ss_tot += (y[i] - y_mean) * (y[i] - y_mean);
   }
   
   r_squared = (ss_tot > 0) ? (1.0 - ss_res / ss_tot) : 0;
   return slope;
}

//+------------------------------------------------------------------+
//| 标准线性回归                                                        |
//+------------------------------------------------------------------+
double CMathUtils::CalculateLinearRegression(const double &x[], const double &y[], int size, double &slope, double &intercept, double &r_squared)
{
   if(size < 2) { slope = 0; intercept = 0; r_squared = 0; return 0; }
   
   double sum_x = 0, sum_y = 0, sum_xy = 0, sum_x2 = 0, sum_y2 = 0;
   for(int i = 0; i < size; i++)
   {
      sum_x += x[i]; sum_y += y[i]; sum_xy += x[i] * y[i];
      sum_x2 += x[i] * x[i]; sum_y2 += y[i] * y[i];
   }
   
   double n = (double)size;
   double denominator = n * sum_x2 - sum_x * sum_x;
   if(MathAbs(denominator) < 1e-10) { slope = 0; intercept = sum_y / n; r_squared = 0; return intercept; }
   
   slope = (n * sum_xy - sum_x * sum_y) / denominator;
   intercept = (sum_y - slope * sum_x) / n;
   
   double ss_tot = sum_y2 - (sum_y * sum_y) / n;
   double ss_res = 0;
   for(int i = 0; i < size; i++)
   {
      double predicted = slope * x[i] + intercept;
      ss_res += (y[i] - predicted) * (y[i] - predicted);
   }
   r_squared = (ss_tot > 0) ? (1.0 - ss_res / ss_tot) : 0;
   return slope;
}

//+------------------------------------------------------------------+
//| 统计函数实现                                                        |
//+------------------------------------------------------------------+
double CMathUtils::CalculateMean(const double &data[], int size)
{
   if(size <= 0) return 0;
   double sum = 0;
   for(int i = 0; i < size; i++) sum += data[i];
   return sum / size;
}

double CMathUtils::CalculateStandardDeviation(const double &data[], int size)
{
   if(size <= 1) return 0;
   double mean = CalculateMean(data, size);
   double sum_sq_diff = 0;
   for(int i = 0; i < size; i++)
   {
      double diff = data[i] - mean;
      sum_sq_diff += diff * diff;
   }
   return MathSqrt(sum_sq_diff / (size - 1));
}

double CMathUtils::CalculateMedian(const double &data[], int size)
{
   if(size <= 0) return 0;
   double sorted_data[];
   ArrayResize(sorted_data, size);
   ArrayCopy(sorted_data, data, 0, 0, size);
   ArraySort(sorted_data);
   
   if(size % 2 == 1)
      return sorted_data[size / 2];
   else
      return (sorted_data[size / 2 - 1] + sorted_data[size / 2]) / 2.0;
}

double CMathUtils::CalculatePercentile(const double &data[], int size, double percentile)
{
   if(size <= 0 || percentile < 0 || percentile > 1) return 0;
   double sorted_data[];
   ArrayResize(sorted_data, size);
   ArrayCopy(sorted_data, data, 0, 0, size);
   ArraySort(sorted_data);
   
   double index = percentile * (size - 1);
   int lower = (int)MathFloor(index);
   int upper = (int)MathCeil(index);
   
   if(lower == upper) return sorted_data[lower];
   
   double weight = index - lower;
   return sorted_data[lower] * (1 - weight) + sorted_data[upper] * weight;
}

//+------------------------------------------------------------------+
//| 核密度估计实现                                                       |
//+------------------------------------------------------------------+
double CMathUtils::GaussianKernel(double u)
{
   return MathExp(-0.5 * u * u) / MathSqrt(2 * M_PI);
}

double CMathUtils::CalculateKDE(const double &data[], int size, double point, double bandwidth = 0)
{
   if(size <= 0) return 0;
   
   if(bandwidth <= 0)
      bandwidth = CalculateStandardDeviation(data, size) * MathPow(size, -0.2);
   
   if(bandwidth <= 0) bandwidth = 0.1;
   
   double density = 0;
   for(int i = 0; i < size; i++)
   {
      double u = (point - data[i]) / bandwidth;
      density += GaussianKernel(u);
   }
   
   return density / (size * bandwidth);
}

void CMathUtils::CalculateKDEArray(const double &data[], int size, const double &points[], int point_count, double &density[], double bandwidth = 0)
{
   ArrayResize(density, point_count);
   for(int i = 0; i < point_count; i++)
   {
      density[i] = CalculateKDE(data, size, points[i], bandwidth);
   }
}

//+------------------------------------------------------------------+
//| 局部极值检测                                                        |
//+------------------------------------------------------------------+
void CMathUtils::FindLocalExtrema(const double &data[], int size, int window, int &peaks[], int &peak_count, int &troughs[], int &trough_count)
{
   peak_count = 0; trough_count = 0;
   
   for(int i = window; i < size - window; i++)
   {
      bool is_peak = true, is_trough = true;
      
      for(int j = i - window; j <= i + window; j++)
      {
         if(j != i)
         {
            if(data[j] >= data[i]) is_peak = false;
            if(data[j] <= data[i]) is_trough = false;
         }
      }
      
      if(is_peak && peak_count < ArraySize(peaks))
         peaks[peak_count++] = i;
      if(is_trough && trough_count < ArraySize(troughs))
         troughs[trough_count++] = i;
   }
}

void CMathUtils::FindSignificantExtrema(const double &data[], int size, double threshold, int &peaks[], int &peak_count, int &troughs[], int &trough_count)
{
   int all_peaks[100], all_troughs[100];
   int all_peak_count, all_trough_count;
   
   FindLocalExtrema(data, size, 3, all_peaks, all_peak_count, all_troughs, all_trough_count);
   
   peak_count = 0; trough_count = 0;
   double volatility = CalculateVolatility(data, size);
   double min_change = volatility * threshold;
   
   // 过滤显著的峰值
   for(int i = 0; i < all_peak_count; i++)
   {
      int idx = all_peaks[i];
      bool significant = false;
      
      // 检查与相邻谷值的差异
      for(int j = 0; j < all_trough_count; j++)
      {
         int trough_idx = all_troughs[j];
         if(MathAbs(idx - trough_idx) <= 10 && MathAbs(data[idx] - data[trough_idx]) > min_change)
         {
            significant = true;
            break;
         }
      }
      
      if(significant && peak_count < ArraySize(peaks))
         peaks[peak_count++] = idx;
   }
   
   // 过滤显著的谷值
   for(int i = 0; i < all_trough_count; i++)
   {
      int idx = all_troughs[i];
      bool significant = false;
      
      for(int j = 0; j < all_peak_count; j++)
      {
         int peak_idx = all_peaks[j];
         if(MathAbs(idx - peak_idx) <= 10 && MathAbs(data[peak_idx] - data[idx]) > min_change)
         {
            significant = true;
            break;
         }
      }
      
      if(significant && trough_count < ArraySize(troughs))
         troughs[trough_count++] = idx;
   }
}

//+------------------------------------------------------------------+
//| 斐波那契计算                                                        |
//+------------------------------------------------------------------+
void CMathUtils::CalculateFibonacciLevels(double high, double low, double &fib_levels[])
{
   ArrayResize(fib_levels, 9);
   double range = high - low;
   
   fib_levels[0] = high;                    // 100%
   fib_levels[1] = high - range * 0.236;    // 76.4%
   fib_levels[2] = high - range * 0.382;    // 61.8%
   fib_levels[3] = high - range * 0.500;    // 50%
   fib_levels[4] = high - range * 0.618;    // 38.2%
   fib_levels[5] = high - range * 0.764;    // 23.6%
   fib_levels[6] = low;                     // 0%
   fib_levels[7] = high + range * 0.618;    // 161.8%
   fib_levels[8] = high + range * 1.000;    // 200%
}

bool CMathUtils::IsAtFibonacciLevel(double price, double high, double low, double tolerance = 0.01)
{
   double fib_levels[9];
   CalculateFibonacciLevels(high, low, fib_levels);
   
   for(int i = 0; i < 9; i++)
   {
      if(MathAbs(price - fib_levels[i]) <= MathAbs(fib_levels[i]) * tolerance)
         return true;
   }
   return false;
}

//+------------------------------------------------------------------+
//| 趋势分析                                                          |
//+------------------------------------------------------------------+
double CMathUtils::CalculateTrendStrength(const double &data[], int size)
{
   if(size < 10) return 0;
   
   double x[], y[];
   ArrayResize(x, size); ArrayResize(y, size);
   
   for(int i = 0; i < size; i++)
   {
      x[i] = i;
      y[i] = data[i];
   }
   
   double slope, intercept, r_squared;
   CalculateLinearRegression(x, y, size, slope, intercept, r_squared);
   
   return r_squared * (slope > 0 ? 1 : -1);
}

bool CMathUtils::IsUptrend(const double &data[], int size, int period = 10)
{
   if(size < period) return false;
   
   double recent_data[];
   ArrayResize(recent_data, period);
   ArrayCopy(recent_data, data, 0, size - period, period);
   
   return CalculateTrendStrength(recent_data, period) > 0.3;
}

bool CMathUtils::IsDowntrend(const double &data[], int size, int period = 10)
{
   if(size < period) return false;
   
   double recent_data[];
   ArrayResize(recent_data, period);
   ArrayCopy(recent_data, data, 0, size - period, period);
   
   return CalculateTrendStrength(recent_data, period) < -0.3;
}

//+------------------------------------------------------------------+
//| 价格分析                                                          |
//+------------------------------------------------------------------+
void CMathUtils::CalculatePriceDistribution(const double &data[], int size, double &support_levels[], double &resistance_levels[], int &level_count)
{
   if(size < 20) { level_count = 0; return; }
   
   // 使用KDE找到价格密集区域
   double min_price = data[0], max_price = data[0];
   for(int i = 1; i < size; i++)
   {
      if(data[i] < min_price) min_price = data[i];
      if(data[i] > max_price) max_price = data[i];
   }
   
   int grid_size = 50;
   double price_grid[], density[];
   ArrayResize(price_grid, grid_size);
   ArrayResize(density, grid_size);
   
   for(int i = 0; i < grid_size; i++)
   {
      price_grid[i] = min_price + (max_price - min_price) * i / (grid_size - 1);
   }
   
   CalculateKDEArray(data, size, price_grid, grid_size, density);
   
   // 找到密度峰值
   int peaks[20], peak_count, troughs[20], trough_count;
   FindLocalExtrema(density, grid_size, 2, peaks, peak_count, troughs, trough_count);
   
   level_count = MathMin(peak_count, 10);
   ArrayResize(support_levels, level_count);
   ArrayResize(resistance_levels, level_count);
   
   for(int i = 0; i < level_count; i++)
   {
      double level_price = price_grid[peaks[i]];
      support_levels[i] = level_price;
      resistance_levels[i] = level_price;
   }
}

double CMathUtils::CalculateVolatility(const double &data[], int size, int period = 20)
{
   if(size < period) return 0;
   
   double returns[];
   ArrayResize(returns, size - 1);
   
   for(int i = 1; i < size; i++)
   {
      returns[i - 1] = MathLog(data[i] / data[i - 1]);
   }
   
   return CalculateStandardDeviation(returns, size - 1) * MathSqrt(252); // 年化波动率
}