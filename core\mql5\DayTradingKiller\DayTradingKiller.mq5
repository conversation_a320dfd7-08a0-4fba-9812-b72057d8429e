//+------------------------------------------------------------------+
//|                                          DayTradingKiller.mq5   |
//|                                    日内交易大杀器 - 终极整合版    |
//|                                    Copyright 2025, Your Company |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Company"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "日内交易大杀器 - 集成开仓、波动率、交易心理、均线系统"

#include "Include/DayTradingKiller_Core.mqh"
#include "Include/DayTradingKiller_UI.mqh"
#include "Include/DayTradingKiller_Psychology.mqh"
#include "Include/DayTradingKiller_Volatility.mqh"
#include "Include/DayTradingKiller_Vegas.mqh"

//--- 输入参数
input group "=== 风险管理设置 ==="
input double Trial_Risk_Percent = 0.2;        // 试仓风险百分比 (%)
input double Standard_Risk_Percent = 0.5;     // 标准仓风险百分比 (%)
input double Heavy_Risk_Percent = 2.0;        // 重仓风险百分比 (%)

input group "=== 技术指标设置 ==="
input int ATR_Period = 14;                    // ATR周期
input double ATR_Multiplier = 2.0;            // ATR乘数
input double Risk_Reward_Ratio = 2.0;         // 风险回报比 (1:X)

input group "=== Vegas均线设置 ==="
input int EMA_Period1 = 20;                   // EMA 1 (短期)
input int EMA_Period2 = 144;                  // EMA 2 (中期1)
input int EMA_Period3 = 169;                  // EMA 3 (中期2)
input int EMA_Period4 = 288;                  // EMA 4 (长期1)
input int EMA_Period5 = 338;                  // EMA 5 (长期2)

input group "=== 交易设置 ==="
input int Magic_Number = 888888;              // 魔术手号码
input int Slippage = 3;                       // 允许滑点 (点)

input group "=== 心理监控设置 ==="
input int LosingStreak_Count = 3;             // 连续亏损警告笔数
input double HeavyPosition_Margin_Percent = 50.0; // 仓位过重警告百分比
input string Upcoming_News_Time = "2024.01.01 00:00"; // 下次重要新闻时间

input group "=== 波动率分析设置 ==="
input int Volatility_AvgDays = 20;            // 计算日均波幅的天数
input bool Show_Session_Analysis = true;      // 显示时段分析

input group "=== 界面设置 ==="
input ENUM_BASE_CORNER Panel_Corner = CORNER_LEFT_UPPER;  // UI面板位置
input color Panel_Background = clrDarkSlateGray;           // 面板背景色
input color Button_Active = clrLimeGreen;                  // 激活按钮颜色
input color Button_Inactive = clrGray;                     // 非激活按钮颜色
input color Text_Color = clrWhite;                         // 文字颜色

//--- 全局变量
CDayTradingCore* g_Core = NULL;
CDayTradingUI* g_UI = NULL;
CDayTradingPsychology* g_Psychology = NULL;
CDayTradingVolatility* g_Volatility = NULL;
CDayTradingVegas* g_Vegas = NULL;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("========================================");
    Print("日内交易大杀器 V1.0 正在启动...");
    Print("========================================");
    
    // 初始化核心交易组件
    g_Core = new CDayTradingCore();
    if(!g_Core.Initialize(Magic_Number, Slippage, ATR_Period, ATR_Multiplier, Risk_Reward_Ratio,
                         Trial_Risk_Percent, Standard_Risk_Percent, Heavy_Risk_Percent))
    {
        Print("核心交易组件初始化失败");
        return INIT_FAILED;
    }
    
    // 初始化UI界面
    g_UI = new CDayTradingUI();
    if(!g_UI.Initialize(Panel_Corner, Panel_Background, Button_Active, 
                       Button_Inactive, Text_Color))
    {
        Print("UI界面初始化失败");
        return INIT_FAILED;
    }
    
    // 初始化交易心理监控
    g_Psychology = new CDayTradingPsychology();
    if(!g_Psychology.Initialize(LosingStreak_Count, HeavyPosition_Margin_Percent, Upcoming_News_Time))
    {
        Print("交易心理监控初始化失败");
        return INIT_FAILED;
    }
    
    // 初始化波动率分析
    g_Volatility = new CDayTradingVolatility();
    if(!g_Volatility.Initialize(Volatility_AvgDays, Show_Session_Analysis))
    {
        Print("波动率分析初始化失败");
        return INIT_FAILED;
    }
    
    // 初始化Vegas均线系统
    g_Vegas = new CDayTradingVegas();
    if(!g_Vegas.Initialize(EMA_Period1, EMA_Period2, EMA_Period3, EMA_Period4, EMA_Period5))
    {
        Print("Vegas均线系统初始化失败");
        return INIT_FAILED;
    }
    
    // 创建完整UI面板
    g_UI.CreateMainPanel();
    
    // 启动定时器
    EventSetTimer(1);
    
    // 初始更新显示
    UpdateAllDisplays();
    
    Print("========================================");
    Print("日内交易大杀器 V1.0 启动成功！");
    Print("功能模块：");
    Print("✓ 一键交易系统");
    Print("✓ 风险管理计算");
    Print("✓ 交易心理监控");
    Print("✓ 波动率分析");
    Print("✓ Vegas均线系统");
    Print("✓ 时段分析");
    Print("========================================");
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // 停止定时器
    EventKillTimer();
    
    // 清理所有组件
    if(g_UI != NULL)
    {
        g_UI.DestroyAllPanels();
        delete g_UI;
        g_UI = NULL;
    }
    
    if(g_Core != NULL)
    {
        delete g_Core;
        g_Core = NULL;
    }
    
    if(g_Psychology != NULL)
    {
        delete g_Psychology;
        g_Psychology = NULL;
    }
    
    if(g_Volatility != NULL)
    {
        delete g_Volatility;
        g_Volatility = NULL;
    }
    
    if(g_Vegas != NULL)
    {
        delete g_Vegas;
        g_Vegas = NULL;
    }
    
    Print("日内交易大杀器已卸载");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // 优化更新频率：每5秒或价格变化超过一定幅度时更新
    static datetime last_update = 0;
    static double last_price = 0;
    
    datetime current_time = TimeCurrent();
    double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    
    // 每5秒更新一次，或价格变化超过0.1%时立即更新
    bool time_update = (current_time - last_update >= 5);
    bool price_update = false;
    
    if(last_price > 0 && current_price > 0)
    {
        price_update = (MathAbs(current_price - last_price) / last_price > 0.001);
    }
    
    if(time_update || price_update || last_price == 0)
    {
        UpdateAllDisplays();
        last_update = current_time;
        last_price = current_price;
    }
}

//+------------------------------------------------------------------+
//| Timer function                                                   |
//+------------------------------------------------------------------+
void OnTimer()
{
    // 更新交易心理监控（每秒更新）
    if(g_Psychology != NULL)
    {
        g_Psychology.UpdatePsychologyState();
    }
    
    // 更新波动率分析（每秒更新）
    if(g_Volatility != NULL)
    {
        g_Volatility.UpdateVolatilityAnalysis();
    }
    
    // 更新Vegas均线分析（每秒更新）
    if(g_Vegas != NULL)
    {
        g_Vegas.UpdateVegasAnalysis();
    }
    
    // 更新UI显示（每秒更新）
    static int timer_count = 0;
    timer_count++;
    
    if(timer_count % 5 == 0) // 每5秒更新一次UI
    {
        UpdateAllDisplays();
    }
}

//+------------------------------------------------------------------+
//| ChartEvent function                                              |
//+------------------------------------------------------------------+
void OnChartEvent(const int id,
                  const long &lparam,
                  const double &dparam,
                  const string &sparam)
{
    if(g_UI == NULL || g_Core == NULL) return;
    
    // 处理UI事件
    if(id == CHARTEVENT_OBJECT_CLICK)
    {
        string clicked_object = sparam;
        
        // 风险档位按钮
        if(clicked_object == "DTK_TrialButton")
        {
            g_Core.SetRiskMode(RISK_TRIAL);
            g_UI.UpdateRiskButtons(RISK_TRIAL);
            UpdateAllDisplays();
        }
        else if(clicked_object == "DTK_StandardButton")
        {
            g_Core.SetRiskMode(RISK_STANDARD);
            g_UI.UpdateRiskButtons(RISK_STANDARD);
            UpdateAllDisplays();
        }
        else if(clicked_object == "DTK_HeavyButton")
        {
            g_Core.SetRiskMode(RISK_HEAVY);
            g_UI.UpdateRiskButtons(RISK_HEAVY);
            UpdateAllDisplays();
        }
        // 市价交易按钮
        else if(clicked_object == "DTK_BuyButton")
        {
            ExecuteTrade(ORDER_TYPE_BUY);
        }
        else if(clicked_object == "DTK_SellButton")
        {
            ExecuteTrade(ORDER_TYPE_SELL);
        }
        // 高点上方挂单按钮
        else if(clicked_object == "DTK_HighBuyButton")
        {
            ExecuteHighPendingOrder(ORDER_TYPE_BUY);
        }
        else if(clicked_object == "DTK_HighSellButton")
        {
            ExecuteHighPendingOrder(ORDER_TYPE_SELL);
        }
        // 低点下方挂单按钮
        else if(clicked_object == "DTK_LowBuyButton")
        {
            ExecuteLowPendingOrder(ORDER_TYPE_BUY);
        }
        else if(clicked_object == "DTK_LowSellButton")
        {
            ExecuteLowPendingOrder(ORDER_TYPE_SELL);
        }
        // 一键平仓按钮
        else if(clicked_object == "DTK_CloseAllButton")
        {
            ExecuteCloseAll();
        }
        
        ChartRedraw();
    }
}

//+------------------------------------------------------------------+
//| 更新所有显示信息                                                  |
//+------------------------------------------------------------------+
void UpdateAllDisplays()
{
    if(g_Core == NULL || g_UI == NULL) return;
    
    // 获取核心计算结果
    STradeCalculation calc;
    if(g_Core.Calculate(calc))
    {
        // 获取心理状态
        SPsychologyState psych_state;
        if(g_Psychology != NULL)
        {
            g_Psychology.GetCurrentState(psych_state);
        }
        
        // 获取波动率分析
        SVolatilityAnalysis vol_analysis;
        if(g_Volatility != NULL)
        {
            g_Volatility.GetCurrentAnalysis(vol_analysis);
        }
        
        // 获取Vegas分析
        SVegasAnalysis vegas_analysis;
        if(g_Vegas != NULL)
        {
            g_Vegas.GetCurrentAnalysis(vegas_analysis);
        }
        
        // 更新UI显示
        g_UI.UpdateMainDisplay(calc, psych_state, vol_analysis, vegas_analysis);
    }
}

//+------------------------------------------------------------------+
//| 执行交易                                                          |
//+------------------------------------------------------------------+
void ExecuteTrade(ENUM_ORDER_TYPE order_type)
{
    if(g_Core == NULL) return;
    
    // 获取最新计算结果
    STradeCalculation calc;
    if(!g_Core.Calculate(calc))
    {
        Print("计算交易参数失败");
        return;
    }
    
    // 检查交易心理状态
    if(g_Psychology != NULL)
    {
        SPsychologyState psych_state;
        g_Psychology.GetCurrentState(psych_state);
        
        // 如果处于危险状态，阻止交易
        if(psych_state.state == STATE_HEAVY_POSITION || psych_state.state == STATE_LOSING_STREAK)
        {
            string warning = "⚠️ 交易心理警告：" + psych_state.warning_message + " - 交易被阻止！";
            Print(warning);
            Comment(warning);
            return;
        }
    }
    
    // 执行交易
    bool result = false;
    if(order_type == ORDER_TYPE_BUY)
    {
        result = g_Core.ExecuteBuy(calc.lot_size, calc.sl_price, calc.tp_price, calc.risk_percent);
    }
    else if(order_type == ORDER_TYPE_SELL)
    {
        result = g_Core.ExecuteSell(calc.lot_size, calc.sl_price, calc.tp_price, calc.risk_percent);
    }
    
    if(result)
    {
        string direction = (order_type == ORDER_TYPE_BUY) ? "买入" : "卖出";
        string success_msg = StringFormat("✅ 交易执行成功: %s %.2f手, SL:%.5f, TP:%.5f", 
                                        direction, calc.lot_size, calc.sl_price, calc.tp_price);
        Print(success_msg);
        Comment(success_msg);
    }
}

//+------------------------------------------------------------------+
//| 执行高点上方挂单交易                                              |
//+------------------------------------------------------------------+
void ExecuteHighPendingOrder(ENUM_ORDER_TYPE order_type)
{
    if(g_Core == NULL) return;
    
    STradeCalculation calc;
    if(!g_Core.Calculate(calc))
    {
        Print("计算交易参数失败");
        return;
    }
    
    bool result = false;
    if(order_type == ORDER_TYPE_BUY)
    {
        result = g_Core.ExecuteHighBuyStop(calc.lot_size, calc.sl_price, calc.tp_price, calc.risk_percent);
    }
    else if(order_type == ORDER_TYPE_SELL)
    {
        result = g_Core.ExecuteHighSellStop(calc.lot_size, calc.sl_price, calc.tp_price, calc.risk_percent);
    }
    
    if(result)
    {
        string direction = (order_type == ORDER_TYPE_BUY) ? "高点挂多" : "高点挂空";
        Print(StringFormat("✅ 挂单执行成功: %s %.2f手, SL:%.5f, TP:%.5f", 
                          direction, calc.lot_size, calc.sl_price, calc.tp_price));
    }
}

//+------------------------------------------------------------------+
//| 执行低点下方挂单交易                                              |
//+------------------------------------------------------------------+
void ExecuteLowPendingOrder(ENUM_ORDER_TYPE order_type)
{
    if(g_Core == NULL) return;
    
    STradeCalculation calc;
    if(!g_Core.Calculate(calc))
    {
        Print("计算交易参数失败");
        return;
    }
    
    bool result = false;
    if(order_type == ORDER_TYPE_BUY)
    {
        result = g_Core.ExecuteLowBuyStop(calc.lot_size, calc.sl_price, calc.tp_price, calc.risk_percent);
    }
    else if(order_type == ORDER_TYPE_SELL)
    {
        result = g_Core.ExecuteLowSellStop(calc.lot_size, calc.sl_price, calc.tp_price, calc.risk_percent);
    }
    
    if(result)
    {
        string direction = (order_type == ORDER_TYPE_BUY) ? "低点挂多" : "低点挂空";
        Print(StringFormat("✅ 挂单执行成功: %s %.2f手, SL:%.5f, TP:%.5f", 
                          direction, calc.lot_size, calc.sl_price, calc.tp_price));
    }
}

//+------------------------------------------------------------------+
//| 执行一键平仓                                                      |
//+------------------------------------------------------------------+
void ExecuteCloseAll()
{
    if(g_Core == NULL) return;
    
    int closed_positions = g_Core.CloseAllPositions();
    if(closed_positions > 0)
    {
        string success_msg = StringFormat("✅ 一键平仓成功：已平仓 %d 个持仓", closed_positions);
        Print(success_msg);
        Comment(success_msg);
    }
    else
    {
        Print("ℹ️ 没有需要平仓的持仓");
    }
}