//+------------------------------------------------------------------+
//|                              DayTradingKiller_Psychology.mqh    |
//|                                    日内交易大杀器 - 交易心理类    |
//|                                    Copyright 2025, Your Company |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Company"
#property link      "https://www.mql5.com"

//--- 交易状态枚举
enum ENUM_TRADE_STATE
{
    STATE_NO_POSITION,           // 空仓等待
    STATE_POSITION_JUST_OPENED,  // 刚刚开仓
    STATE_POSITION_IN_PROFIT,    // 持仓盈利
    STATE_POSITION_LOSING,       // 持仓亏损
    STATE_HEAVY_POSITION,        // 仓位过重
    STATE_LOSING_STREAK,         // 连续亏损
    STATE_HUGE_WIN,              // 巨大盈利
    STATE_LOW_VOLATILITY,        // 低波动
    STATE_APPROACHING_NEWS       // 临近新闻
};

//--- 交易模式枚举
enum ENUM_TRADING_MODE
{
    MODE_DAY_SNIPER,    // 日内狙击手模式
    MODE_SWING_HUNTER   // 波段猎人模式
};

//--- 心理状态结构
struct SPsychologyState
{
    ENUM_TRADE_STATE state;
    string warning_message;
    string status_text;
    color status_color;
    bool allow_trading;
    double danger_level;  // 0-100 危险等级
};

//+------------------------------------------------------------------+
//| 交易心理监控类                                                    |
//+------------------------------------------------------------------+
class CDayTradingPsychology
{
private:
    int m_losing_streak_count;
    double m_heavy_position_margin_percent;
    string m_upcoming_news_time;
    
    // 状态跟踪变量
    datetime m_last_position_time;
    int m_consecutive_losses;
    double m_last_balance;
    bool m_position_just_opened;
    datetime m_position_open_time;
    
    // 心理状态缓存
    SPsychologyState m_current_state;
    datetime m_last_update_time;
    
public:
    CDayTradingPsychology();
    ~CDayTradingPsychology();
    
    bool Initialize(int losing_streak_count, double heavy_position_margin_percent, string upcoming_news_time);
    void UpdatePsychologyState();
    void GetCurrentState(SPsychologyState &state);
    bool IsTradeAllowed();
    
private:
    ENUM_TRADE_STATE GetTradeState();
    ENUM_TRADING_MODE GetTradingMode();
    double GetTotalProfit();
    bool IsHeavyPosition();
    bool IsLowVolatility();
    bool IsApproachingNews();
    void CheckRecentTrades();
    void CheckNewPosition();
    string GetPsychologyText(ENUM_TRADE_STATE state, ENUM_TRADING_MODE mode);
    void GetStateColors(ENUM_TRADE_STATE state, color &status_color, double &danger_level);
    string GetStateStatusText(ENUM_TRADE_STATE state);
};

//+------------------------------------------------------------------+
//| 构造函数                                                          |
//+------------------------------------------------------------------+
CDayTradingPsychology::CDayTradingPsychology()
{
    m_losing_streak_count = 3;
    m_heavy_position_margin_percent = 50.0;
    m_upcoming_news_time = "2024.01.01 00:00";
    
    m_last_position_time = 0;
    m_consecutive_losses = 0;
    m_last_balance = 0;
    m_position_just_opened = false;
    m_position_open_time = 0;
    m_last_update_time = 0;
    
    // 初始化状态
    m_current_state.state = STATE_NO_POSITION;
    m_current_state.warning_message = "系统初始化中...";
    m_current_state.status_text = "正常";
    m_current_state.status_color = clrLightGreen;
    m_current_state.allow_trading = true;
    m_current_state.danger_level = 0;
}

//+------------------------------------------------------------------+
//| 析构函数                                                          |
//+------------------------------------------------------------------+
CDayTradingPsychology::~CDayTradingPsychology()
{
}

//+------------------------------------------------------------------+
//| 初始化                                                            |
//+------------------------------------------------------------------+
bool CDayTradingPsychology::Initialize(int losing_streak_count, double heavy_position_margin_percent, string upcoming_news_time)
{
    m_losing_streak_count = losing_streak_count;
    m_heavy_position_margin_percent = heavy_position_margin_percent;
    m_upcoming_news_time = upcoming_news_time;
    
    // 初始化账户余额
    m_last_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    
    Print("交易心理监控初始化成功");
    return true;
}

//+------------------------------------------------------------------+
//| 更新心理状态                                                      |
//+------------------------------------------------------------------+
void CDayTradingPsychology::UpdatePsychologyState()
{
    // 检测新开仓
    CheckNewPosition();
    
    // 获取当前状态和模式
    ENUM_TRADE_STATE current_state = GetTradeState();
    ENUM_TRADING_MODE current_mode = GetTradingMode();
    
    // 更新状态结构
    m_current_state.state = current_state;
    m_current_state.warning_message = GetPsychologyText(current_state, current_mode);
    m_current_state.status_text = GetStateStatusText(current_state);
    
    // 获取状态颜色和危险等级
    GetStateColors(current_state, m_current_state.status_color, m_current_state.danger_level);
    
    // 判断是否允许交易
    m_current_state.allow_trading = (current_state != STATE_HEAVY_POSITION && 
                                    current_state != STATE_LOSING_STREAK);
    
    m_last_update_time = TimeCurrent();
}

//+------------------------------------------------------------------+
//| 获取当前状态                                                      |
//+------------------------------------------------------------------+
void CDayTradingPsychology::GetCurrentState(SPsychologyState &state)
{
    state = m_current_state;
}

//+------------------------------------------------------------------+
//| 是否允许交易                                                      |
//+------------------------------------------------------------------+
bool CDayTradingPsychology::IsTradeAllowed()
{
    return m_current_state.allow_trading;
}

//+------------------------------------------------------------------+
//| 获取当前交易状态                                                  |
//+------------------------------------------------------------------+
ENUM_TRADE_STATE CDayTradingPsychology::GetTradeState()
{
    // 检查是否有持仓
    if(PositionsTotal() == 0)
    {
        // 检查是否刚刚平仓
        CheckRecentTrades();
        
        // 检查连续亏损
        if(m_consecutive_losses >= m_losing_streak_count)
            return STATE_LOSING_STREAK;
            
        // 检查低波动
        if(IsLowVolatility())
            return STATE_LOW_VOLATILITY;
            
        // 检查临近新闻
        if(IsApproachingNews())
            return STATE_APPROACHING_NEWS;
            
        return STATE_NO_POSITION;
    }
    
    // 有持仓的情况
    double total_profit = GetTotalProfit();
    
    // 检查仓位是否过重
    if(IsHeavyPosition())
        return STATE_HEAVY_POSITION;
    
    // 检查是否刚刚开仓
    if(m_position_just_opened && TimeCurrent() - m_position_open_time < 300) // 5分钟内
        return STATE_POSITION_JUST_OPENED;
    
    // 检查盈亏状态
    if(total_profit > 0)
    {
        if(total_profit > AccountInfoDouble(ACCOUNT_BALANCE) * 0.05) // 盈利超过5%
            return STATE_HUGE_WIN;
        return STATE_POSITION_IN_PROFIT;
    }
    else if(total_profit < 0)
    {
        return STATE_POSITION_LOSING;
    }
    
    return STATE_NO_POSITION;
}

//+------------------------------------------------------------------+
//| 获取当前交易模式                                                  |
//+------------------------------------------------------------------+
ENUM_TRADING_MODE CDayTradingPsychology::GetTradingMode()
{
    ENUM_TIMEFRAMES current_period = Period();
    
    // 小于等于1小时为日内狙击手模式
    if(current_period <= PERIOD_H1)
        return MODE_DAY_SNIPER;
    else
        return MODE_SWING_HUNTER;
}

//+------------------------------------------------------------------+
//| 获取总盈亏                                                        |
//+------------------------------------------------------------------+
double CDayTradingPsychology::GetTotalProfit()
{
    double total_profit = 0;
    for(int i = 0; i < PositionsTotal(); i++)
    {
        ulong ticket = PositionGetTicket(i);
        if(ticket > 0 && PositionSelectByTicket(ticket))
        {
            total_profit += PositionGetDouble(POSITION_PROFIT);
        }
    }
    return total_profit;
}

//+------------------------------------------------------------------+
//| 检查是否仓位过重                                                  |
//+------------------------------------------------------------------+
bool CDayTradingPsychology::IsHeavyPosition()
{
    double margin_used = AccountInfoDouble(ACCOUNT_MARGIN);
    double equity = AccountInfoDouble(ACCOUNT_EQUITY);
    
    if(equity <= 0) return false;
    
    double margin_percent = (margin_used / equity) * 100;
    return margin_percent > m_heavy_position_margin_percent;
}

//+------------------------------------------------------------------+
//| 检查是否低波动                                                    |
//+------------------------------------------------------------------+
bool CDayTradingPsychology::IsLowVolatility()
{
    // 简化判断：基于最近几根K线的价格波动
    double high_prices[], low_prices[];
    int bars_to_check = 10;
    
    if(CopyHigh(_Symbol, _Period, 0, bars_to_check, high_prices) < bars_to_check ||
       CopyLow(_Symbol, _Period, 0, bars_to_check, low_prices) < bars_to_check)
        return false;
    
    double total_range = 0;
    for(int i = 0; i < bars_to_check; i++)
    {
        total_range += (high_prices[i] - low_prices[i]);
    }
    
    double avg_range = total_range / bars_to_check;
    double current_range = high_prices[0] - low_prices[0];
    
    // 当前波动小于平均波动的60%认为是低波动
    return current_range < avg_range * 0.6;
}

//+------------------------------------------------------------------+
//| 检查是否临近新闻                                                  |
//+------------------------------------------------------------------+
bool CDayTradingPsychology::IsApproachingNews()
{
    datetime news_time = StringToTime(m_upcoming_news_time);
    datetime current_time = TimeCurrent();
    
    // 新闻前15分钟内
    return (news_time - current_time) <= 900 && (news_time - current_time) > 0;
}

//+------------------------------------------------------------------+
//| 检查最近交易记录                                                  |
//+------------------------------------------------------------------+
void CDayTradingPsychology::CheckRecentTrades()
{
    // 检查历史交易，统计连续亏损
    HistorySelect(TimeCurrent() - 86400, TimeCurrent()); // 最近24小时
    
    int total_deals = HistoryDealsTotal();
    int recent_losses = 0;
    
    for(int i = total_deals - 1; i >= 0; i--)
    {
        ulong ticket = HistoryDealGetTicket(i);
        if(ticket > 0)
        {
            if(HistoryDealGetInteger(ticket, DEAL_ENTRY) == DEAL_ENTRY_OUT)
            {
                double profit = HistoryDealGetDouble(ticket, DEAL_PROFIT);
                if(profit < 0)
                    recent_losses++;
                else
                    break; // 遇到盈利交易就停止计数
            }
        }
    }
    
    m_consecutive_losses = recent_losses;
}

//+------------------------------------------------------------------+
//| 检测新开仓                                                        |
//+------------------------------------------------------------------+
void CDayTradingPsychology::CheckNewPosition()
{
    static int last_positions_count = 0;
    int current_positions_count = PositionsTotal();
    
    if(current_positions_count > last_positions_count)
    {
        m_position_just_opened = true;
        m_position_open_time = TimeCurrent();
    }
    else if(current_positions_count == 0)
    {
        m_position_just_opened = false;
    }
    
    last_positions_count = current_positions_count;
}

//+------------------------------------------------------------------+
//| 获取心理提示文本                                                  |
//+------------------------------------------------------------------+
string CDayTradingPsychology::GetPsychologyText(ENUM_TRADE_STATE state, ENUM_TRADING_MODE mode)
{
    string text = "";
    
    switch(state)
    {
        case STATE_NO_POSITION:
            if(mode == MODE_DAY_SNIPER)
            {
                string texts[] = {
                    "【空仓等待】你现在很安全！不要因无聊而开仓！",
                    "【耐心测试】手又痒了？这就是亏钱原因！",
                    "【纪律考验】没信号就是最好信号，告诉你不要交易！"
                };
                text = texts[MathRand() % ArraySize(texts)];
            }
            else
            {
                string texts[] = {
                    "【波段等待】等一个大机会！不是小打小闹！",
                    "【趋势未现】现在进场是赌博！你是来赚钱的！",
                    "【资本保护】空仓是最好朋友！比盈利更重要！"
                };
                text = texts[MathRand() % ArraySize(texts)];
            }
            break;
            
        case STATE_POSITION_JUST_OPENED:
            {
                string texts[] = {
                    "【刚开仓】现在最危险！不要调整止损！",
                    "【执行阶段】计划已定，执行！聪明想法是愚蠢！"
                };
                text = texts[MathRand() % ArraySize(texts)];
            }
            break;
            
        case STATE_POSITION_IN_PROFIT:
            if(mode == MODE_DAY_SNIPER)
            {
                text = "【日内盈利】不要高兴太早！坚持到目标位！";
            }
            else
            {
                string texts[] = {
                    "【波段盈利】这才刚开始！敌人是贪婪！",
                    "【趋势进行中】盈利让你飘了？让利润奔跑！"
                };
                text = texts[MathRand() % ArraySize(texts)];
            }
            break;
            
        case STATE_POSITION_LOSING:
            {
                string texts[] = {
                    "【浮亏警告】想扛单？想加仓？这会让你破产！",
                    "【止损准备】亏损是交易一部分！执行止损！",
                    "【现实检查】市场说你错了！承认错误离场！"
                };
                text = texts[MathRand() % ArraySize(texts)];
            }
            break;
            
        case STATE_HEAVY_POSITION:
            text = "🚨【仓位过重】🚨 你疯了吗？这不是交易！这是自杀！";
            break;
            
        case STATE_LOSING_STREAK:
            {
                string texts[] = {
                    "🔥【连亏" + IntegerToString(m_consecutive_losses) + "笔】🔥 停手！失控了！今天到此为止！",
                    "💀【情绪失控】💀 你是亏钱机器！关电脑冷静！"
                };
                text = texts[MathRand() % ArraySize(texts)];
            }
            break;
            
        case STATE_HUGE_WIN:
            {
                string texts[] = {
                    "⚠️【大赚警告】⚠️ 你现在很危险！回归纪律！",
                    "🎯【成功陷阱】🎯 觉得自己天才？市场教你做人！"
                };
                text = texts[MathRand() % ArraySize(texts)];
            }
            break;
            
        case STATE_LOW_VOLATILITY:
            if(mode == MODE_DAY_SNIPER)
            {
                text = "😴【市场沉睡】😴 现在交易烧钱！手续费赚不回！";
            }
            else
            {
                text = "📊【横盘整理】📊 耐心！大行情酝酿！进场送钱！";
            }
            break;
            
        case STATE_APPROACHING_NEWS:
            text = "📢【新闻来袭】📢 15分钟后重要数据！立即平仓！";
            break;
    }
    
    return text;
}

//+------------------------------------------------------------------+
//| 获取状态对应的颜色和危险等级                                      |
//+------------------------------------------------------------------+
void CDayTradingPsychology::GetStateColors(ENUM_TRADE_STATE state, color &status_color, double &danger_level)
{
    switch(state)
    {
        case STATE_HEAVY_POSITION:
        case STATE_LOSING_STREAK:
            status_color = clrRed;
            danger_level = 100;
            break;
            
        case STATE_POSITION_LOSING:
        case STATE_APPROACHING_NEWS:
            status_color = clrOrange;
            danger_level = 70;
            break;
            
        case STATE_HUGE_WIN:
            status_color = clrYellow;
            danger_level = 50;
            break;
            
        case STATE_POSITION_JUST_OPENED:
            status_color = clrLightBlue;
            danger_level = 30;
            break;
            
        case STATE_POSITION_IN_PROFIT:
            status_color = clrLightGreen;
            danger_level = 10;
            break;
            
        case STATE_NO_POSITION:
        case STATE_LOW_VOLATILITY:
        default:
            status_color = clrLightGreen;
            danger_level = 0;
            break;
    }
}

//+------------------------------------------------------------------+
//| 获取状态文本                                                      |
//+------------------------------------------------------------------+
string CDayTradingPsychology::GetStateStatusText(ENUM_TRADE_STATE state)
{
    switch(state)
    {
        case STATE_NO_POSITION:
            return "空仓等待";
        case STATE_POSITION_JUST_OPENED:
            return "刚刚开仓";
        case STATE_POSITION_IN_PROFIT:
            return "持仓盈利";
        case STATE_POSITION_LOSING:
            return "持仓亏损";
        case STATE_HEAVY_POSITION:
            return "⚠️ 仓位过重";
        case STATE_LOSING_STREAK:
            return "🚨 连续亏损";
        case STATE_HUGE_WIN:
            return "巨大盈利";
        case STATE_LOW_VOLATILITY:
            return "低波动期";
        case STATE_APPROACHING_NEWS:
            return "📢 临近新闻";
        default:
            return "未知状态";
    }
}
