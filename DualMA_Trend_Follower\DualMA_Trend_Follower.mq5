//+------------------------------------------------------------------+
//|                                      DualMA_Trend_Follower.mq5 |
//|                                  Copyright 2024, CodeBuddy Team |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, CodeBuddy Team"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "基于EMA(20)和EMA(90)双均线的回调趋势跟踪系统，使用ATR移动止盈"

#include <Trade\Trade.mqh>

//+------------------------------------------------------------------+
//| 市场状态枚举
//+------------------------------------------------------------------+
enum ENUM_MARKET_REGIME
{
   REGIME_STRONG_UP,    // 强多头趋势
   REGIME_STRONG_DOWN,  // 强空头趋势
   REGIME_CHOPPY        // 震荡/无趋势
};

//+------------------------------------------------------------------+
//| EA 输入参数
//+------------------------------------------------------------------+
input group "=== 核心指标参数 ===";
input int    Fast_EMA_Period = 20;       // 快速EMA周期 (河流)
input int    Slow_EMA_Period = 90;       // 慢速EMA周期 (洋流)
input int    ATR_Period = 14;            // ATR周期 (用于止损)

input group "=== 交易逻辑参数 ===";
input int    Trend_Lookback = 5;         // 均线方向确认的回看周期
input double Stop_Loss_ATR_Multiplier = 1.5; // 初始止损的ATR倍数
input double Trailing_Stop_ATR_Multiplier = 2.5; // ATR移动止盈倍数 (Chandelier Exit)

input group "=== 风险与资金管理 ===";
input double Risk_Per_Trade_Percent = 1.0; // 单笔交易风险 (账户百分比)

input group "=== 加仓模块参数 (可选) ===";
input bool   Enable_Pyramiding = false;    // 是否启用浮盈加仓
input double Pyramiding_Trigger_RR = 2.0;  // 触发加仓的盈亏比 (R:R)
input int    Max_Pyramiding_Count = 2;     // 最大加仓次数

//+------------------------------------------------------------------+
//| 持仓跟踪结构体
//+------------------------------------------------------------------+
struct PositionTracker
{
   ulong ticket;                // 持仓票据
   double highest_high;         // 多头持仓的最高价
   double lowest_low;           // 空头持仓的最低价
   double entry_price;          // 入场价格
   datetime entry_time;         // 入场时间
   ENUM_POSITION_TYPE pos_type; // 持仓类型
   bool is_valid;               // 跟踪记录是否有效
};

//+------------------------------------------------------------------+
//| 全局变量
//+------------------------------------------------------------------+
CTrade trade;                    // 交易对象
int fast_ema_handle;            // 快速EMA句柄
int slow_ema_handle;            // 慢速EMA句柄
int atr_handle;                 // ATR句柄

double fast_ema[];              // 快速EMA数组
double slow_ema[];              // 慢速EMA数组
double atr[];                   // ATR数组

datetime last_bar_time = 0;     // 上一根K线时间
int position_count = 0;         // 当前持仓数量

PositionTracker position_trackers[];  // 持仓跟踪数组

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // 初始化指标句柄
   fast_ema_handle = iMA(_Symbol, PERIOD_CURRENT, Fast_EMA_Period, 0, MODE_EMA, PRICE_CLOSE);
   slow_ema_handle = iMA(_Symbol, PERIOD_CURRENT, Slow_EMA_Period, 0, MODE_EMA, PRICE_CLOSE);
   atr_handle = iATR(_Symbol, PERIOD_CURRENT, ATR_Period);
   
   // 检查句柄有效性
   if(fast_ema_handle == INVALID_HANDLE || slow_ema_handle == INVALID_HANDLE || atr_handle == INVALID_HANDLE)
   {
      Print("指标句柄初始化失败");
      return INIT_FAILED;
   }
   
   // 设置数组为时间序列
   ArraySetAsSeries(fast_ema, true);
   ArraySetAsSeries(slow_ema, true);
   ArraySetAsSeries(atr, true);
   
   // 初始化持仓跟踪数组
   ArrayResize(position_trackers, 0);
   
   Print("DualMA_Trend_Follower EA 初始化成功 - 使用ATR移动止盈");
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // 释放指标句柄
   if(fast_ema_handle != INVALID_HANDLE) IndicatorRelease(fast_ema_handle);
   if(slow_ema_handle != INVALID_HANDLE) IndicatorRelease(slow_ema_handle);
   if(atr_handle != INVALID_HANDLE) IndicatorRelease(atr_handle);
   
   Print("DualMA_Trend_Follower EA 已卸载");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // 更新指标数据
   if(!UpdateIndicatorData()) return;
   
   // 每个tick都更新持仓跟踪和检查移动止损
   UpdatePositionTracking();
   
   // 如果有持仓，每个tick都检查ATR移动止损
   if(position_count > 0)
   {
      CheckATRTrailingStop();
   }
   
   // 检查是否有新K线
   datetime current_bar_time = iTime(_Symbol, PERIOD_CURRENT, 0);
   bool is_new_bar = (current_bar_time != last_bar_time);
   
   if(is_new_bar)
   {
      last_bar_time = current_bar_time;
      
      // 如果没有持仓，检查入场信号
      if(position_count == 0)
      {
         CheckForEntrySignal();
      }
      else
      {
         // 检查加仓机会
         if(Enable_Pyramiding && position_count < Max_Pyramiding_Count)
         {
            CheckPyramidingOpportunity();
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 更新指标数据
//+------------------------------------------------------------------+
bool UpdateIndicatorData()
{
   // 复制指标数据
   if(CopyBuffer(fast_ema_handle, 0, 0, Trend_Lookback + 10, fast_ema) <= 0) return false;
   if(CopyBuffer(slow_ema_handle, 0, 0, Trend_Lookback + 10, slow_ema) <= 0) return false;
   if(CopyBuffer(atr_handle, 0, 0, 5, atr) <= 0) return false;
   
   return true;
}

//+------------------------------------------------------------------+
//| 更新持仓跟踪 - 修正版
//+------------------------------------------------------------------+
void UpdatePositionTracking()
{
   // 首先标记所有跟踪记录为无效
   for(int i = 0; i < ArraySize(position_trackers); i++)
   {
      position_trackers[i].is_valid = false;
   }
   
   position_count = 0;
   
   // 遍历当前持仓
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == _Symbol)
      {
         ulong ticket = PositionGetTicket(i);
         ENUM_POSITION_TYPE pos_type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
         double entry_price = PositionGetDouble(POSITION_PRICE_OPEN);
         datetime entry_time = (datetime)PositionGetInteger(POSITION_TIME);
         
         // 查找现有跟踪记录
         int tracker_index = FindPositionTracker(ticket);
         
         if(tracker_index == -1)
         {
            // 新持仓，创建跟踪记录
            AddPositionTracker(ticket, pos_type, entry_price, entry_time);
         }
         else
         {
            // 标记为有效并更新极值
            position_trackers[tracker_index].is_valid = true;
            UpdatePositionExtremes(tracker_index);
         }
         
         position_count++;
      }
   }
   
   // 清理无效的跟踪记录
   CleanupInvalidTrackers();
}

//+------------------------------------------------------------------+
//| 查找持仓跟踪记录
//+------------------------------------------------------------------+
int FindPositionTracker(ulong ticket)
{
   for(int i = 0; i < ArraySize(position_trackers); i++)
   {
      if(position_trackers[i].ticket == ticket)
         return i;
   }
   return -1;
}

//+------------------------------------------------------------------+
//| 添加持仓跟踪记录
//+------------------------------------------------------------------+
void AddPositionTracker(ulong ticket, ENUM_POSITION_TYPE pos_type, double entry_price, datetime entry_time)
{
   int size = ArraySize(position_trackers);
   ArrayResize(position_trackers, size + 1);
   
   position_trackers[size].ticket = ticket;
   position_trackers[size].pos_type = pos_type;
   position_trackers[size].entry_price = entry_price;
   position_trackers[size].entry_time = entry_time;
   position_trackers[size].is_valid = true;
   
   // 获取当前价格作为初始极值
   double current_bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   double current_ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   
   if(pos_type == POSITION_TYPE_BUY)
   {
      position_trackers[size].highest_high = MathMax(entry_price, current_bid);
      position_trackers[size].lowest_low = 0;
   }
   else
   {
      position_trackers[size].highest_high = 0;
      position_trackers[size].lowest_low = MathMin(entry_price, current_ask);
   }
   
   Print(StringFormat("添加持仓跟踪: 票据 %d, 类型 %s, 入场价 %.5f, 初始极值 %.5f", 
                     ticket, 
                     pos_type == POSITION_TYPE_BUY ? "多头" : "空头", 
                     entry_price,
                     pos_type == POSITION_TYPE_BUY ? position_trackers[size].highest_high : position_trackers[size].lowest_low));
}

//+------------------------------------------------------------------+
//| 更新持仓极值 - 修正版
//+------------------------------------------------------------------+
void UpdatePositionExtremes(int tracker_index)
{
   // 使用当前市场价格而不是历史K线价格
   double current_bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   double current_ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   
   if(position_trackers[tracker_index].pos_type == POSITION_TYPE_BUY)
   {
      // 多头持仓，更新最高价（使用bid价格）
      if(current_bid > position_trackers[tracker_index].highest_high)
      {
         double old_high = position_trackers[tracker_index].highest_high;
         position_trackers[tracker_index].highest_high = current_bid;
         Print(StringFormat("多头极值更新: 票据 %d, 新高 %.5f (原 %.5f)", 
                           position_trackers[tracker_index].ticket, current_bid, old_high));
      }
   }
   else
   {
      // 空头持仓，更新最低价（使用ask价格）
      if(current_ask < position_trackers[tracker_index].lowest_low || position_trackers[tracker_index].lowest_low == 0)
      {
         double old_low = position_trackers[tracker_index].lowest_low;
         position_trackers[tracker_index].lowest_low = current_ask;
         Print(StringFormat("空头极值更新: 票据 %d, 新低 %.5f (原 %.5f)", 
                           position_trackers[tracker_index].ticket, current_ask, old_low));
      }
   }
}

//+------------------------------------------------------------------+
//| 清理无效的跟踪记录
//+------------------------------------------------------------------+
void CleanupInvalidTrackers()
{
   for(int i = ArraySize(position_trackers) - 1; i >= 0; i--)
   {
      if(!position_trackers[i].is_valid)
      {
         Print(StringFormat("清理无效跟踪记录: 票据 %d", position_trackers[i].ticket));
         
         // 移除无效记录
         for(int j = i; j < ArraySize(position_trackers) - 1; j++)
         {
            position_trackers[j] = position_trackers[j + 1];
         }
         ArrayResize(position_trackers, ArraySize(position_trackers) - 1);
      }
   }
}

//+------------------------------------------------------------------+
//| 获取市场状态
//+------------------------------------------------------------------+
ENUM_MARKET_REGIME GetMarketRegime()
{
   // 检查快慢均线位置关系
   bool fast_above_slow = fast_ema[1] > slow_ema[1];
   
   // 检查快速均线趋势方向
   bool fast_ema_rising = fast_ema[1] > fast_ema[1 + Trend_Lookback];
   
   // 检查慢速均线趋势方向
   bool slow_ema_rising = slow_ema[1] > slow_ema[1 + Trend_Lookback];
   
   // 判断市场状态
   if(fast_above_slow && fast_ema_rising && slow_ema_rising)
   {
      return REGIME_STRONG_UP;
   }
   else if(!fast_above_slow && !fast_ema_rising && !slow_ema_rising)
   {
      return REGIME_STRONG_DOWN;
   }
   else
   {
      return REGIME_CHOPPY;
   }
}

//+------------------------------------------------------------------+
//| 检查入场信号
//+------------------------------------------------------------------+
void CheckForEntrySignal()
{
   ENUM_MARKET_REGIME regime = GetMarketRegime();
   
   // 如果市场处于震荡状态，不交易
   if(regime == REGIME_CHOPPY) return;
   
   // 检查做多信号
   if(regime == REGIME_STRONG_UP)
   {
      if(CheckLongEntrySignal())
      {
         double sl_price = slow_ema[1] - (Stop_Loss_ATR_Multiplier * atr[1]);
         ExecuteTrade(ORDER_TYPE_BUY, sl_price);
      }
   }
   // 检查做空信号
   else if(regime == REGIME_STRONG_DOWN)
   {
      if(CheckShortEntrySignal())
      {
         double sl_price = slow_ema[1] + (Stop_Loss_ATR_Multiplier * atr[1]);
         ExecuteTrade(ORDER_TYPE_SELL, sl_price);
      }
   }
}

//+------------------------------------------------------------------+
//| 检查做多入场信号
//+------------------------------------------------------------------+
bool CheckLongEntrySignal()
{
   // 检查回调确认：过去K线中价格曾进入双均线之间
   bool pullback_confirmed = false;
   for(int i = 2; i <= 10; i++) // 检查过去10根K线
   {
      double low_price = iLow(_Symbol, PERIOD_CURRENT, i);
      if(low_price < fast_ema[i] && low_price > slow_ema[i])
      {
         pullback_confirmed = true;
         break;
      }
   }
   
   if(!pullback_confirmed) return false;
   
   // 入场触发：收盘价突破前一根K线高点
   double close_1 = iClose(_Symbol, PERIOD_CURRENT, 1);
   double high_2 = iHigh(_Symbol, PERIOD_CURRENT, 2);
   
   return close_1 > high_2;
}

//+------------------------------------------------------------------+
//| 检查做空入场信号
//+------------------------------------------------------------------+
bool CheckShortEntrySignal()
{
   // 检查回调确认：过去K线中价格曾进入双均线之间
   bool pullback_confirmed = false;
   for(int i = 2; i <= 10; i++) // 检查过去10根K线
   {
      double high_price = iHigh(_Symbol, PERIOD_CURRENT, i);
      if(high_price > fast_ema[i] && high_price < slow_ema[i])
      {
         pullback_confirmed = true;
         break;
      }
   }
   
   if(!pullback_confirmed) return false;
   
   // 入场触发：收盘价跌破前一根K线低点
   double close_1 = iClose(_Symbol, PERIOD_CURRENT, 1);
   double low_2 = iLow(_Symbol, PERIOD_CURRENT, 2);
   
   return close_1 < low_2;
}

//+------------------------------------------------------------------+
//| 计算头寸大小
//+------------------------------------------------------------------+
double CalculatePositionSize(double stop_loss_price)
{
   double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   double risk_amount = account_balance * Risk_Per_Trade_Percent / 100.0;
   
   double current_price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   double stop_distance = MathAbs(current_price - stop_loss_price);
   
   if(stop_distance <= 0) return 0;
   
   double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
   double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
   double point_value = tick_value * (SymbolInfoDouble(_Symbol, SYMBOL_POINT) / tick_size);
   
   double lot_size = risk_amount / (stop_distance / SymbolInfoDouble(_Symbol, SYMBOL_POINT) * point_value);
   
   // 标准化手数
   double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
   double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
   double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
   
   lot_size = MathMax(min_lot, MathMin(max_lot, NormalizeDouble(lot_size / lot_step, 0) * lot_step));
   
   return lot_size;
}

//+------------------------------------------------------------------+
//| 执行交易
//+------------------------------------------------------------------+
void ExecuteTrade(ENUM_ORDER_TYPE order_type, double sl_price)
{
   double lot_size = CalculatePositionSize(sl_price);
   if(lot_size <= 0) return;
   
   string comment = StringFormat("DualMA_%s_Pos%d", 
                                order_type == ORDER_TYPE_BUY ? "Long" : "Short", 
                                position_count + 1);
   
   bool result = false;
   if(order_type == ORDER_TYPE_BUY)
   {
      result = trade.Buy(lot_size, _Symbol, 0, sl_price, 0, comment);
   }
   else
   {
      result = trade.Sell(lot_size, _Symbol, 0, sl_price, 0, comment);
   }
   
   if(result)
   {
      Print(StringFormat("交易执行成功: %s %.2f手, 止损: %.5f", 
                        order_type == ORDER_TYPE_BUY ? "买入" : "卖出", 
                        lot_size, sl_price));
   }
   else
   {
      Print("交易执行失败: ", trade.ResultRetcodeDescription());
   }
}

//+------------------------------------------------------------------+
//| 检查ATR移动止损 - 修正版
//+------------------------------------------------------------------+
void CheckATRTrailingStop()
{
   double current_atr = atr[1];
   
   for(int i = 0; i < ArraySize(position_trackers); i++)
   {
      if(!position_trackers[i].is_valid) continue;
      
      ulong ticket = position_trackers[i].ticket;
      ENUM_POSITION_TYPE pos_type = position_trackers[i].pos_type;
      
      // 选择持仓以获取当前止损价
      if(!PositionSelectByTicket(ticket)) continue;
      
      double current_sl = PositionGetDouble(POSITION_SL);
      double current_tp = PositionGetDouble(POSITION_TP);
      double new_trailing_stop_level = 0;
      bool should_update = false;
      
      if(pos_type == POSITION_TYPE_BUY)
      {
         // 多头持仓：移动止损 = 最高价 - N * ATR
         new_trailing_stop_level = position_trackers[i].highest_high - (Trailing_Stop_ATR_Multiplier * current_atr);
         
         // 只有当新止损价高于当前止损价时才更新（多单止损只上移）
         if(new_trailing_stop_level > current_sl)
         {
            should_update = true;
         }
      }
      else
      {
         // 空头持仓：移动止损 = 最低价 + N * ATR
         new_trailing_stop_level = position_trackers[i].lowest_low + (Trailing_Stop_ATR_Multiplier * current_atr);
         
         // 只有当新止损价低于当前止损价时才更新（空单止损只下移）
         if(new_trailing_stop_level < current_sl || current_sl == 0)
         {
            should_update = true;
         }
      }
      
      if(should_update)
      {
         if(trade.PositionModify(ticket, new_trailing_stop_level, current_tp))
         {
            Print(StringFormat("【ATR移动止损更新成功】票据 %d, %s, 新止损 %.5f, 原止损 %.5f, 极值价 %.5f, ATR %.5f", 
                              ticket, 
                              pos_type == POSITION_TYPE_BUY ? "多头" : "空头",
                              new_trailing_stop_level, 
                              current_sl,
                              pos_type == POSITION_TYPE_BUY ? position_trackers[i].highest_high : position_trackers[i].lowest_low,
                              current_atr));
         }
         else
         {
            Print(StringFormat("ATR移动止损更新失败: 票据 %d, 错误: %s", ticket, trade.ResultRetcodeDescription()));
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 检查加仓机会
//+------------------------------------------------------------------+
void CheckPyramidingOpportunity()
{
   // 计算当前总浮动盈利
   double total_profit = 0;
   double initial_risk = 0;
   
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) != _Symbol) continue;
      
      total_profit += PositionGetDouble(POSITION_PROFIT);
      
      // 估算初始风险（基于当前持仓）
      double pos_volume = PositionGetDouble(POSITION_VOLUME);
      double pos_price = PositionGetDouble(POSITION_PRICE_OPEN);
      double pos_sl = PositionGetDouble(POSITION_SL);
      
      if(pos_sl > 0)
      {
         double risk_per_position = MathAbs(pos_price - pos_sl) * pos_volume * 
                                   SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE) / 
                                   SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
         initial_risk += risk_per_position;
      }
   }
   
   // 检查是否达到加仓条件
   if(initial_risk > 0 && total_profit >= initial_risk * Pyramiding_Trigger_RR)
   {
      // 重新检查入场信号进行加仓
      ENUM_MARKET_REGIME regime = GetMarketRegime();
      
      if(regime == REGIME_STRONG_UP && CheckLongEntrySignal())
      {
         double sl_price = slow_ema[1] - (Stop_Loss_ATR_Multiplier * atr[1]);
         ExecuteTrade(ORDER_TYPE_BUY, sl_price);
         UpdateAllStopLosses(sl_price, POSITION_TYPE_BUY);
      }
      else if(regime == REGIME_STRONG_DOWN && CheckShortEntrySignal())
      {
         double sl_price = slow_ema[1] + (Stop_Loss_ATR_Multiplier * atr[1]);
         ExecuteTrade(ORDER_TYPE_SELL, sl_price);
         UpdateAllStopLosses(sl_price, POSITION_TYPE_SELL);
      }
   }
}

//+------------------------------------------------------------------+
//| 更新所有同方向持仓的止损
//+------------------------------------------------------------------+
void UpdateAllStopLosses(double new_sl, ENUM_POSITION_TYPE pos_type)
{
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) != _Symbol) continue;
      
      if((ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE) == pos_type)
      {
         ulong ticket = PositionGetTicket(i);
         double current_sl = PositionGetDouble(POSITION_SL);
         double current_tp = PositionGetDouble(POSITION_TP);
         
         // 只有当新止损更有利时才更新
         bool should_update = false;
         if(pos_type == POSITION_TYPE_BUY && new_sl > current_sl)
         {
            should_update = true;
         }
         else if(pos_type == POSITION_TYPE_SELL && new_sl < current_sl)
         {
            should_update = true;
         }
         
         if(should_update)
         {
            if(trade.PositionModify(ticket, new_sl, current_tp))
            {
               Print(StringFormat("更新止损成功: 票据 %d, 新止损: %.5f", ticket, new_sl));
            }
         }
      }
   }
}