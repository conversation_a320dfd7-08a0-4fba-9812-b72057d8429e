//+------------------------------------------------------------------+
//|                                       DayTradeSignalModule.mqh |
//|                                  Copyright 2024, TradingSystem |
//|                                    日内交易信号模块             |
//+------------------------------------------------------------------+

#ifndef DAY_TRADE_SIGNAL_MODULE_H
#define DAY_TRADE_SIGNAL_MODULE_H

//+------------------------------------------------------------------+
//| 日内交易信号模块类                                               |
//+------------------------------------------------------------------+
class CDayTradeSignalModule
{
private:
    // 参数设置
    int m_emaPeriod;                    // EMA周期
    double m_breakoutMinPips;           // 最小突破点数
    double m_volumeMultiplier;          // 成交量放大倍数
    
    // 指标句柄
    int m_triggerEMA;                   // 触发周期EMA句柄
    
    // 内部方法
    bool IsBreakoutValid(string symbol, ENUM_TIMEFRAMES tf);
    bool IsVolumeConfirmed(string symbol, ENUM_TIMEFRAMES tf);
    bool IsStrongCandlePresent(string symbol, ENUM_TIMEFRAMES tf);
    double GetAverageVolume(string symbol, ENUM_TIMEFRAMES tf, int periods);
    bool IsBullishCandle(string symbol, ENUM_TIMEFRAMES tf, int shift);

public:
    // 构造函数和析构函数
    CDayTradeSignalModule();
    ~CDayTradeSignalModule();
    
    // 初始化方法
    bool Initialize(int emaPeriod, double breakoutMinPips, double volumeMultiplier);
    
    // 主要信号检测方法 - 对应Checklist C1和C2
    bool IsTriggerBreakoutConfirmed(string symbol, ENUM_TIMEFRAMES tf);    // C1. 5分钟图突破EMA20
    bool IsTriggerConfirmationValid(string symbol, ENUM_TIMEFRAMES tf);    // C2. 5分钟图突破确认
    
    // 辅助方法
    double GetCurrentEMAValue(string symbol, ENUM_TIMEFRAMES tf);
    void PrintSignalStatus(string symbol, ENUM_TIMEFRAMES tf);
};

//+------------------------------------------------------------------+
//| 构造函数                                                         |
//+------------------------------------------------------------------+
CDayTradeSignalModule::CDayTradeSignalModule()
{
    m_emaPeriod = 20;
    m_breakoutMinPips = 5.0;
    m_volumeMultiplier = 1.2;
    m_triggerEMA = INVALID_HANDLE;
}

//+------------------------------------------------------------------+
//| 析构函数                                                         |
//+------------------------------------------------------------------+
CDayTradeSignalModule::~CDayTradeSignalModule()
{
    if(m_triggerEMA != INVALID_HANDLE)
        IndicatorRelease(m_triggerEMA);
}

//+------------------------------------------------------------------+
//| 初始化方法                                                       |
//+------------------------------------------------------------------+
bool CDayTradeSignalModule::Initialize(int emaPeriod, double breakoutMinPips, double volumeMultiplier)
{
    m_emaPeriod = emaPeriod;
    m_breakoutMinPips = breakoutMinPips;
    m_volumeMultiplier = volumeMultiplier;
    
    // 创建EMA指标句柄 (将在具体调用时创建)
    Print("日内交易信号模块初始化成功");
    Print("EMA周期: ", m_emaPeriod);
    Print("最小突破点数: ", m_breakoutMinPips);
    Print("成交量倍数: ", m_volumeMultiplier);
    
    return true;
}

//+------------------------------------------------------------------+
//| C1. 检查5分钟图是否突破EMA20                                     |
//+------------------------------------------------------------------+
bool CDayTradeSignalModule::IsTriggerBreakoutConfirmed(string symbol, ENUM_TIMEFRAMES tf)
{
    // 创建或获取EMA句柄
    if(m_triggerEMA == INVALID_HANDLE)
    {
        m_triggerEMA = iMA(symbol, tf, m_emaPeriod, 0, MODE_EMA, PRICE_CLOSE);
        if(m_triggerEMA == INVALID_HANDLE)
        {
            Print("创建触发EMA句柄失败");
            return false;
        }
    }
    
    // 获取当前价格和EMA值
    double currentPrice = iClose(symbol, tf, 0);
    double previousPrice = iClose(symbol, tf, 1);
    
    double emaBuffer[2];
    if(CopyBuffer(m_triggerEMA, 0, 0, 2, emaBuffer) <= 0)
    {
        Print("获取EMA数据失败");
        return false;
    }
    
    double currentEMA = emaBuffer[0];
    double previousEMA = emaBuffer[1];
    
    // 检查突破条件
    // 1. 当前价格在EMA之上
    bool priceAboveEMA = currentPrice > currentEMA;
    
    // 2. 前一根K线价格在EMA之下或接近EMA
    bool previousBelowEMA = previousPrice <= previousEMA * 1.001; // 允许0.1%的误差
    
    // 3. 突破幅度足够
    double breakoutPips = (currentPrice - currentEMA) / SymbolInfoDouble(symbol, SYMBOL_POINT);
    bool sufficientBreakout = breakoutPips >= m_breakoutMinPips;
    
    bool breakoutConfirmed = priceAboveEMA && previousBelowEMA && sufficientBreakout;
    
    if(breakoutConfirmed)
    {
        Print("✅ 5分钟图EMA20突破确认:");
        Print("   当前价格: ", currentPrice, " EMA: ", currentEMA);
        Print("   突破点数: ", breakoutPips);
    }
    
    return breakoutConfirmed;
}

//+------------------------------------------------------------------+
//| C2. 检查5分钟图突破确认                                          |
//+------------------------------------------------------------------+
bool CDayTradeSignalModule::IsTriggerConfirmationValid(string symbol, ENUM_TIMEFRAMES tf)
{
    bool confirmationValid = true;
    
    // 1. 检查成交量放大 (如果需要)
    if(m_volumeMultiplier > 1.0)
    {
        bool volumeConfirmed = IsVolumeConfirmed(symbol, tf);
        if(!volumeConfirmed)
        {
            Print("❌ 成交量未放大确认");
            confirmationValid = false;
        }
        else
        {
            Print("✅ 成交量放大确认");
        }
    }
    
    // 2. 检查标志性阳线 (如果需要)
    if(InpRequireStrongCandle)
    {
        bool strongCandle = IsStrongCandlePresent(symbol, tf);
        if(!strongCandle)
        {
            Print("❌ 未出现标志性阳线");
            confirmationValid = false;
        }
        else
        {
            Print("✅ 出现标志性阳线");
        }
    }
    
    // 3. 检查突破有效性
    bool breakoutValid = IsBreakoutValid(symbol, tf);
    if(!breakoutValid)
    {
        Print("❌ 突破无效");
        confirmationValid = false;
    }
    else
    {
        Print("✅ 突破有效");
    }
    
    return confirmationValid;
}

//+------------------------------------------------------------------+
//| 检查突破有效性                                                   |
//+------------------------------------------------------------------+
bool CDayTradeSignalModule::IsBreakoutValid(string symbol, ENUM_TIMEFRAMES tf)
{
    // 获取最近3根K线数据
    double closes[3], highs[3], lows[3];
    
    if(CopyClose(symbol, tf, 0, 3, closes) <= 0 ||
       CopyHigh(symbol, tf, 0, 3, highs) <= 0 ||
       CopyLow(symbol, tf, 0, 3, lows) <= 0)
        return false;
    
    // 检查价格是否持续在突破位之上
    double currentEMA = GetCurrentEMAValue(symbol, tf);
    if(currentEMA == 0) return false;
    
    // 当前K线和前一根K线都应该在EMA之上
    bool currentAbove = closes[0] > currentEMA;
    bool previousAbove = closes[1] > currentEMA * 0.999; // 允许微小偏差
    
    // 检查是否有假突破 (价格快速回落到EMA之下)
    bool noFalseBreakout = lows[0] > currentEMA * 0.995; // 最低价不能大幅跌破EMA
    
    return currentAbove && previousAbove && noFalseBreakout;
}

//+------------------------------------------------------------------+
//| 检查成交量确认                                                   |
//+------------------------------------------------------------------+
bool CDayTradeSignalModule::IsVolumeConfirmed(string symbol, ENUM_TIMEFRAMES tf)
{
    // 获取当前成交量
    long currentVolume = iVolume(symbol, tf, 0);
    if(currentVolume <= 0) return true; // 如果无法获取成交量，跳过检查
    
    // 获取平均成交量
    double avgVolume = GetAverageVolume(symbol, tf, 20);
    if(avgVolume <= 0) return true;
    
    // 检查成交量是否放大
    double volumeRatio = (double)currentVolume / avgVolume;
    
    return (volumeRatio >= m_volumeMultiplier);
}

//+------------------------------------------------------------------+
//| 检查标志性阳线                                                   |
//+------------------------------------------------------------------+
bool CDayTradeSignalModule::IsStrongCandlePresent(string symbol, ENUM_TIMEFRAMES tf)
{
    // 检查当前K线是否为强势阳线
    double open = iOpen(symbol, tf, 0);
    double close = iClose(symbol, tf, 0);
    double high = iHigh(symbol, tf, 0);
    double low = iLow(symbol, tf, 0);
    
    if(open <= 0 || close <= 0 || high <= 0 || low <= 0)
        return false;
    
    // 1. 必须是阳线
    bool isBullish = close > open;
    
    // 2. 实体相对较大 (实体占整个K线的60%以上)
    double bodySize = close - open;
    double totalSize = high - low;
    bool strongBody = (totalSize > 0) && (bodySize / totalSize >= 0.6);
    
    // 3. 上影线相对较短 (上影线不超过实体的50%)
    double upperShadow = high - close;
    bool shortUpperShadow = (bodySize > 0) && (upperShadow / bodySize <= 0.5);
    
    return isBullish && strongBody && shortUpperShadow;
}

//+------------------------------------------------------------------+
//| 获取平均成交量                                                   |
//+------------------------------------------------------------------+
double CDayTradeSignalModule::GetAverageVolume(string symbol, ENUM_TIMEFRAMES tf, int periods)
{
    long volumes[];
    ArraySetAsSeries(volumes, true);
    
    if(CopyTickVolume(symbol, tf, 1, periods, volumes) <= 0) // 从第1根开始，排除当前K线
        return 0;
    
    long totalVolume = 0;
    for(int i = 0; i < periods; i++)
    {
        totalVolume += volumes[i];
    }
    
    return (double)totalVolume / periods;
}

//+------------------------------------------------------------------+
//| 获取当前EMA值                                                    |
//+------------------------------------------------------------------+
double CDayTradeSignalModule::GetCurrentEMAValue(string symbol, ENUM_TIMEFRAMES tf)
{
    if(m_triggerEMA == INVALID_HANDLE)
    {
        m_triggerEMA = iMA(symbol, tf, m_emaPeriod, 0, MODE_EMA, PRICE_CLOSE);
        if(m_triggerEMA == INVALID_HANDLE)
            return 0;
    }
    
    double buffer[1];
    if(CopyBuffer(m_triggerEMA, 0, 0, 1, buffer) <= 0)
        return 0;
    
    return buffer[0];
}

//+------------------------------------------------------------------+
//| 打印信号状态                                                     |
//+------------------------------------------------------------------+
void CDayTradeSignalModule::PrintSignalStatus(string symbol, ENUM_TIMEFRAMES tf)
{
    Print("=== 触发信号状态 ===");
    Print("品种: ", symbol, " 周期: ", EnumToString(tf));
    Print("EMA突破: ", IsTriggerBreakoutConfirmed(symbol, tf) ? "是" : "否");
    Print("突破确认: ", IsTriggerConfirmationValid(symbol, tf) ? "是" : "否");
    Print("==================");
}

#endif // DAY_TRADE_SIGNAL_MODULE_H
