//+------------------------------------------------------------------+
//|                                  DayTradingKiller_Core.mqh      |
//|                                    日内交易大杀器 - 核心交易类    |
//|                                    Copyright 2025, Your Company |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Company"
#property link      "https://www.mql5.com"

#include <Trade\Trade.mqh>

//--- 风险模式枚举
enum ENUM_RISK_MODE
{
    RISK_TRIAL = 0,     // 试仓
    RISK_STANDARD = 1,  // 标准仓
    RISK_HEAVY = 2      // 重仓
};

//--- 交易计算结果结构
struct STradeCalculation
{
    ENUM_RISK_MODE risk_mode;      // 当前风险模式
    double risk_percent;           // 风险百分比
    double lot_size;               // 计算手数
    double sl_price;               // 止损价格
    double tp_price;               // 止盈价格
    double estimated_loss;         // 预估亏损
    double estimated_profit;       // 预估盈利
    double atr_value;              // ATR值
    double sl_distance;            // 止损距离
    double tp_distance;            // 止盈距离
    double current_price;          // 当前价格
    double account_balance;        // 账户余额
    double account_equity;         // 账户净值
    double free_margin;            // 可用保证金
    int total_positions;           // 当前持仓数
};

//+------------------------------------------------------------------+
//| 日内交易核心类                                                    |
//+------------------------------------------------------------------+
class CDayTradingCore
{
private:
    CTrade m_trade;
    int m_magic_number;
    int m_slippage;
    
    // ATR指标相关
    int m_atr_handle;
    int m_atr_period;
    double m_atr_multiplier;
    double m_risk_reward_ratio;
    
    // 风险管理参数
    double m_trial_risk;
    double m_standard_risk;
    double m_heavy_risk;
    ENUM_RISK_MODE m_current_mode;
    
    // 缓存的Symbol信息
    double m_tick_size;
    double m_min_lot;
    double m_max_lot;
    double m_lot_step;
    double m_point_value;
    
public:
    CDayTradingCore();
    ~CDayTradingCore();
    
    bool Initialize(int magic_number, int slippage, int atr_period, double atr_multiplier, 
                   double rrr, double trial_risk, double standard_risk, double heavy_risk);
    bool Calculate(STradeCalculation &calc);
    void SetRiskMode(ENUM_RISK_MODE mode);
    ENUM_RISK_MODE GetRiskMode() const { return m_current_mode; }
    
    // 交易执行函数
    bool ExecuteBuy(double lot_size, double sl_price, double tp_price, double risk_percent);
    bool ExecuteSell(double lot_size, double sl_price, double tp_price, double risk_percent);
    bool ExecuteHighBuyStop(double lot_size, double sl_price, double tp_price, double risk_percent);
    bool ExecuteHighSellStop(double lot_size, double sl_price, double tp_price, double risk_percent);
    bool ExecuteLowBuyStop(double lot_size, double sl_price, double tp_price, double risk_percent);
    bool ExecuteLowSellStop(double lot_size, double sl_price, double tp_price, double risk_percent);
    int CloseAllPositions();
    
private:
    void CacheSymbolInfo();
    bool ValidateTradeParameters(double lot_size, double sl_price, double tp_price);
    void LogTradeError(int error_code, string operation);
    double GetCurrentBarHigh();
    double GetCurrentBarLow();
    double GetATRValue();
    double GetCurrentRiskPercent();
    double NormalizeLotSize(double lot_size);
    double CalculatePointValue();
    string GetRiskModeName(ENUM_RISK_MODE mode);
};

//+------------------------------------------------------------------+
//| 构造函数                                                          |
//+------------------------------------------------------------------+
CDayTradingCore::CDayTradingCore()
{
    m_magic_number = 0;
    m_slippage = 3;
    m_atr_handle = INVALID_HANDLE;
    m_atr_period = 14;
    m_atr_multiplier = 2.0;
    m_risk_reward_ratio = 2.0;
    m_trial_risk = 0.2;
    m_standard_risk = 0.5;
    m_heavy_risk = 2.0;
    m_current_mode = RISK_STANDARD;
}

//+------------------------------------------------------------------+
//| 析构函数                                                          |
//+------------------------------------------------------------------+
CDayTradingCore::~CDayTradingCore()
{
    if(m_atr_handle != INVALID_HANDLE)
        IndicatorRelease(m_atr_handle);
}

//+------------------------------------------------------------------+
//| 初始化                                                            |
//+------------------------------------------------------------------+
bool CDayTradingCore::Initialize(int magic_number, int slippage, int atr_period, double atr_multiplier, 
                                double rrr, double trial_risk, double standard_risk, double heavy_risk)
{
    m_magic_number = magic_number;
    m_slippage = slippage;
    m_atr_period = atr_period;
    m_atr_multiplier = atr_multiplier;
    m_risk_reward_ratio = rrr;
    m_trial_risk = trial_risk;
    m_standard_risk = standard_risk;
    m_heavy_risk = heavy_risk;
    
    // 设置交易参数
    m_trade.SetExpertMagicNumber(m_magic_number);
    m_trade.SetDeviationInPoints(m_slippage);
    m_trade.SetTypeFilling(ORDER_FILLING_FOK);
    m_trade.SetTypeFillingBySymbol(Symbol());
    
    // 创建ATR指标
    m_atr_handle = iATR(Symbol(), PERIOD_CURRENT, m_atr_period);
    if(m_atr_handle == INVALID_HANDLE)
    {
        Print("创建ATR指标失败");
        return false;
    }
    
    // 缓存Symbol信息
    CacheSymbolInfo();
    
    return true;
}

//+------------------------------------------------------------------+
//| 缓存Symbol信息                                                    |
//+------------------------------------------------------------------+
void CDayTradingCore::CacheSymbolInfo()
{
    m_tick_size = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);
    m_min_lot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
    m_max_lot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX);
    m_lot_step = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_STEP);
    m_point_value = CalculatePointValue();
}

//+------------------------------------------------------------------+
//| 计算交易参数                                                      |
//+------------------------------------------------------------------+
bool CDayTradingCore::Calculate(STradeCalculation &calc)
{
    // 获取ATR值
    double atr_value = GetATRValue();
    if(atr_value <= 0)
        return false;
    
    // 获取当前价格
    double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    if(current_price <= 0)
        return false;
    
    // 计算止损距离
    double sl_distance = atr_value * m_atr_multiplier;
    double tp_distance = sl_distance * m_risk_reward_ratio;
    
    // 获取当前风险百分比
    double risk_percent = GetCurrentRiskPercent();
    
    // 计算风险金额
    double account_equity = AccountInfoDouble(ACCOUNT_EQUITY);
    double risk_amount = account_equity * risk_percent / 100.0;
    
    // 计算手数
    double lot_size;
    double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
    if(point <= 0 || sl_distance <= 0 || m_point_value <= 0)
    {
        lot_size = m_min_lot;
    }
    else
    {
        double denominator = (sl_distance / point) * m_point_value;
        if(denominator <= 0)
        {
            lot_size = m_min_lot;
        }
        else
        {
            lot_size = risk_amount / denominator;
        }
    }
    
    lot_size = NormalizeLotSize(lot_size);
    
    // 填充结果结构
    calc.risk_mode = m_current_mode;
    calc.risk_percent = risk_percent;
    calc.lot_size = lot_size;
    calc.atr_value = atr_value;
    calc.sl_distance = sl_distance;
    calc.tp_distance = tp_distance;
    calc.current_price = current_price;
    
    // 计算买入的止损止盈价格（默认显示买入价格）
    calc.sl_price = current_price - sl_distance;
    calc.tp_price = current_price + tp_distance;
    
    // 计算预估盈亏
    calc.estimated_loss = sl_distance / point * m_point_value * lot_size;
    calc.estimated_profit = tp_distance / point * m_point_value * lot_size;
    
    // 账户信息
    calc.account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    calc.account_equity = account_equity;
    calc.free_margin = AccountInfoDouble(ACCOUNT_MARGIN_FREE);
    calc.total_positions = PositionsTotal();
    
    return true;
}

//+------------------------------------------------------------------+
//| 设置风险模式                                                      |
//+------------------------------------------------------------------+
void CDayTradingCore::SetRiskMode(ENUM_RISK_MODE mode)
{
    m_current_mode = mode;
    Print("风险模式切换为: ", GetRiskModeName(mode));
}

//+------------------------------------------------------------------+
//| 执行买入交易                                                      |
//+------------------------------------------------------------------+
bool CDayTradingCore::ExecuteBuy(double lot_size, double sl_price, double tp_price, double risk_percent)
{
    if(!ValidateTradeParameters(lot_size, sl_price, tp_price))
        return false;
    
    double ask = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    
    string comment = StringFormat("DTK多头|%.1f%%|%.2f手", risk_percent, lot_size);
    bool result = m_trade.Buy(lot_size, Symbol(), ask, sl_price, tp_price, comment);
    
    if(!result)
    {
        LogTradeError(m_trade.ResultRetcode(), "买入");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 执行卖出交易                                                      |
//+------------------------------------------------------------------+
bool CDayTradingCore::ExecuteSell(double lot_size, double sl_price, double tp_price, double risk_percent)
{
    if(!ValidateTradeParameters(lot_size, sl_price, tp_price))
        return false;
    
    double bid = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    
    // 对于卖出订单，重新计算止损止盈
    double sl_distance = MathAbs(sl_price - bid);
    double tp_distance = MathAbs(tp_price - bid);
    
    double sell_sl = bid + sl_distance;  // 卖出止损在当前价上方
    double sell_tp = bid - tp_distance;  // 卖出止盈在当前价下方
    
    string comment = StringFormat("DTK空头|%.1f%%|%.2f手", risk_percent, lot_size);
    bool result = m_trade.Sell(lot_size, Symbol(), bid, sell_sl, sell_tp, comment);
    
    if(!result)
    {
        LogTradeError(m_trade.ResultRetcode(), "卖出");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 执行高点上方买入挂单（突破做多）                                  |
//+------------------------------------------------------------------+
bool CDayTradingCore::ExecuteHighBuyStop(double lot_size, double sl_price, double tp_price, double risk_percent)
{
    if(!ValidateTradeParameters(lot_size, sl_price, tp_price))
        return false;
    
    double current_high = GetCurrentBarHigh();
    double entry_price = current_high + m_tick_size;
    
    double current_price = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    double sl_distance = MathAbs(sl_price - current_price);
    double tp_distance = MathAbs(tp_price - current_price);
    
    double buy_sl = entry_price - sl_distance;
    double buy_tp = entry_price + tp_distance;
    
    string comment = StringFormat("DTK高点挂多|%.1f%%|%.2f手", risk_percent, lot_size);
    bool result = m_trade.BuyStop(lot_size, entry_price, Symbol(), buy_sl, buy_tp, 
                                 ORDER_TIME_GTC, 0, comment);
    
    if(!result)
    {
        LogTradeError(m_trade.ResultRetcode(), "高点挂单买入");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 执行高点上方卖出挂单（假突破做空）                                |
//+------------------------------------------------------------------+
bool CDayTradingCore::ExecuteHighSellStop(double lot_size, double sl_price, double tp_price, double risk_percent)
{
    if(!ValidateTradeParameters(lot_size, sl_price, tp_price))
        return false;
    
    double current_high = GetCurrentBarHigh();
    double entry_price = current_high + m_tick_size;
    
    double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double sl_distance = MathAbs(sl_price - current_price);
    double tp_distance = MathAbs(tp_price - current_price);
    
    double sell_sl = entry_price + sl_distance;
    double sell_tp = entry_price - tp_distance;
    
    string comment = StringFormat("DTK高点挂空|%.1f%%|%.2f手", risk_percent, lot_size);
    bool result = m_trade.SellLimit(lot_size, entry_price, Symbol(), sell_sl, sell_tp, 
                                   ORDER_TIME_GTC, 0, comment);
    
    if(!result)
    {
        LogTradeError(m_trade.ResultRetcode(), "高点挂单卖出");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 执行低点下方买入挂单（假跌破做多）                                |
//+------------------------------------------------------------------+
bool CDayTradingCore::ExecuteLowBuyStop(double lot_size, double sl_price, double tp_price, double risk_percent)
{
    if(!ValidateTradeParameters(lot_size, sl_price, tp_price))
        return false;
    
    double current_low = GetCurrentBarLow();
    double entry_price = current_low - m_tick_size;
    
    double current_price = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    double sl_distance = MathAbs(sl_price - current_price);
    double tp_distance = MathAbs(tp_price - current_price);
    
    double buy_sl = entry_price - sl_distance;
    double buy_tp = entry_price + tp_distance;
    
    string comment = StringFormat("DTK低点挂多|%.1f%%|%.2f手", risk_percent, lot_size);
    bool result = m_trade.BuyLimit(lot_size, entry_price, Symbol(), buy_sl, buy_tp, 
                                  ORDER_TIME_GTC, 0, comment);
    
    if(!result)
    {
        LogTradeError(m_trade.ResultRetcode(), "低点挂单买入");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 执行低点下方卖出挂单（跌破做空）                                  |
//+------------------------------------------------------------------+
bool CDayTradingCore::ExecuteLowSellStop(double lot_size, double sl_price, double tp_price, double risk_percent)
{
    if(!ValidateTradeParameters(lot_size, sl_price, tp_price))
        return false;
    
    double current_low = GetCurrentBarLow();
    double entry_price = current_low - m_tick_size;
    
    double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double sl_distance = MathAbs(sl_price - current_price);
    double tp_distance = MathAbs(tp_price - current_price);
    
    double sell_sl = entry_price + sl_distance;
    double sell_tp = entry_price - tp_distance;
    
    string comment = StringFormat("DTK低点挂空|%.1f%%|%.2f手", risk_percent, lot_size);
    bool result = m_trade.SellStop(lot_size, entry_price, Symbol(), sell_sl, sell_tp, 
                                  ORDER_TIME_GTC, 0, comment);
    
    if(!result)
    {
        LogTradeError(m_trade.ResultRetcode(), "低点挂单卖出");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 一键平仓所有持仓                                                  |
//+------------------------------------------------------------------+
int CDayTradingCore::CloseAllPositions()
{
    int closed_count = 0;
    
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        ulong ticket = PositionGetTicket(i);
        if(ticket > 0 && PositionSelectByTicket(ticket))
        {
            if(PositionGetInteger(POSITION_MAGIC) == m_magic_number)
            {
                if(m_trade.PositionClose(ticket))
                {
                    closed_count++;
                    Print("平仓成功: 票据 ", ticket);
                }
                else
                {
                    LogTradeError(m_trade.ResultRetcode(), "平仓");
                }
            }
        }
    }
    
    return closed_count;
}

//+------------------------------------------------------------------+
//| 获取前一根K线最高价                                               |
//+------------------------------------------------------------------+
double CDayTradingCore::GetCurrentBarHigh()
{
    double high_buffer[1];
    if(CopyHigh(Symbol(), PERIOD_CURRENT, 1, 1, high_buffer) <= 0)
        return 0;
    
    return high_buffer[0];
}

//+------------------------------------------------------------------+
//| 获取前一根K线最低价                                               |
//+------------------------------------------------------------------+
double CDayTradingCore::GetCurrentBarLow()
{
    double low_buffer[1];
    if(CopyLow(Symbol(), PERIOD_CURRENT, 1, 1, low_buffer) <= 0)
        return 0;
    
    return low_buffer[0];
}

//+------------------------------------------------------------------+
//| 获取ATR值                                                         |
//+------------------------------------------------------------------+
double CDayTradingCore::GetATRValue()
{
    if(m_atr_handle == INVALID_HANDLE)
        return 0;
    
    double atr_buffer[1];
    if(CopyBuffer(m_atr_handle, 0, 0, 1, atr_buffer) <= 0)
        return 0;
    
    return atr_buffer[0];
}

//+------------------------------------------------------------------+
//| 获取当前风险百分比                                                |
//+------------------------------------------------------------------+
double CDayTradingCore::GetCurrentRiskPercent()
{
    switch(m_current_mode)
    {
        case RISK_TRIAL:
            return m_trial_risk;
        case RISK_STANDARD:
            return m_standard_risk;
        case RISK_HEAVY:
            return m_heavy_risk;
        default:
            return m_standard_risk;
    }
}

//+------------------------------------------------------------------+
//| 标准化手数                                                        |
//+------------------------------------------------------------------+
double CDayTradingCore::NormalizeLotSize(double lot_size)
{
    // 确保手数不小于最小值
    if(lot_size < m_min_lot)
        lot_size = m_min_lot;
    
    // 确保手数不大于最大值
    if(lot_size > m_max_lot)
        lot_size = m_max_lot;
    
    // 按步长调整
    lot_size = MathRound(lot_size / m_lot_step) * m_lot_step;
    
    return lot_size;
}

//+------------------------------------------------------------------+
//| 计算点值                                                          |
//+------------------------------------------------------------------+
double CDayTradingCore::CalculatePointValue()
{
    double tick_value = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE);
    double tick_size = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);
    double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
    
    if(tick_size <= 0 || point <= 0)
    {
        return 1.0;
    }
    
    return tick_value * point / tick_size;
}

//+------------------------------------------------------------------+
//| 获取风险模式名称                                                  |
//+------------------------------------------------------------------+
string CDayTradingCore::GetRiskModeName(ENUM_RISK_MODE mode)
{
    switch(mode)
    {
        case RISK_TRIAL:
            return "试仓";
        case RISK_STANDARD:
            return "标准仓";
        case RISK_HEAVY:
            return "重仓";
        default:
            return "标准仓";
    }
}

//+------------------------------------------------------------------+
//| 验证交易参数                                                      |
//+------------------------------------------------------------------+
bool CDayTradingCore::ValidateTradeParameters(double lot_size, double sl_price, double tp_price)
{
    // 检查手数
    if(lot_size < m_min_lot || lot_size > m_max_lot)
    {
        Print(StringFormat("手数超出范围: %.2f (允许范围: %.2f - %.2f)", lot_size, m_min_lot, m_max_lot));
        return false;
    }
    
    // 检查账户余额
    double margin_required = 0;
    if(!OrderCalcMargin(ORDER_TYPE_BUY, Symbol(), lot_size, SymbolInfoDouble(Symbol(), SYMBOL_ASK), margin_required))
    {
        Print("无法计算保证金需求");
        return false;
    }
    
    double free_margin = AccountInfoDouble(ACCOUNT_MARGIN_FREE);
    if(margin_required > free_margin)
    {
        Print(StringFormat("保证金不足: 需要%.2f, 可用%.2f", margin_required, free_margin));
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 记录交易错误                                                      |
//+------------------------------------------------------------------+
void CDayTradingCore::LogTradeError(int error_code, string operation)
{
    string error_desc = "";
    
    switch(error_code)
    {
        case TRADE_RETCODE_REQUOTE:
            error_desc = "重新报价";
            break;
        case TRADE_RETCODE_REJECT:
            error_desc = "请求被拒绝";
            break;
        case TRADE_RETCODE_MARKET_CLOSED:
            error_desc = "市场关闭";
            break;
        case TRADE_RETCODE_INVALID_VOLUME:
            error_desc = "无效手数";
            break;
        case TRADE_RETCODE_NO_MONEY:
            error_desc = "资金不足";
            break;
        case TRADE_RETCODE_INVALID_STOPS:
            error_desc = "无效止损/止盈";
            break;
        default:
            error_desc = "未知错误";
            break;
    }
    
    Print(StringFormat("%s交易失败: %s (错误代码: %d)", operation, error_desc, error_code));
}
