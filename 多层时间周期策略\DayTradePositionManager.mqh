//+------------------------------------------------------------------+
//|                                    DayTradePositionManager.mqh |
//|                                  Copyright 2024, TradingSystem |
//|                                    日内交易仓位管理模块         |
//+------------------------------------------------------------------+

#ifndef DAY_TRADE_POSITION_MANAGER_H
#define DAY_TRADE_POSITION_MANAGER_H

#include <Trade\Trade.mqh>

//+------------------------------------------------------------------+
//| 持仓信息结构                                                     |
//+------------------------------------------------------------------+
struct SPositionInfo
{
    ulong ticket;                   // 订单号
    datetime openTime;              // 开仓时间
    double openPrice;               // 开仓价格
    double lotSize;                 // 手数
    double stopLoss;                // 止损价格
    double takeProfit;              // 止盈价格
    ENUM_POSITION_TYPE type;        // 持仓类型
    string symbol;                  // 品种
};

//+------------------------------------------------------------------+
//| 日内交易仓位管理类                                               |
//+------------------------------------------------------------------+
class CDayTradePositionManager
{
private:
    // 基础设置
    int m_magicNumber;              // 魔术号
    int m_maxPositionTime;          // 最大持仓时间(分钟)
    string m_tradeComment;          // 交易备注
    
    // 交易对象
    CTrade m_trade;                 // 交易类实例
    
    // 持仓管理
    SPositionInfo m_positions[];    // 持仓信息数组
    
    // 内部方法
    bool UpdatePositionInfo();
    bool IsPositionExpired(const SPositionInfo& pos);
    bool ClosePosition(ulong ticket, string reason);
    void LogPositionAction(string action, const SPositionInfo& pos, string reason = "");

public:
    // 构造函数和析构函数
    CDayTradePositionManager();
    ~CDayTradePositionManager();
    
    // 初始化方法
    bool Initialize(int magicNumber, int maxPositionTime);
    
    // 主要仓位管理方法
    bool OpenPosition(string symbol, ENUM_ORDER_TYPE orderType, double lotSize, 
                     double stopLoss, double takeProfit);
    bool CloseAllPositions(string reason = "");
    bool ManageExistingPositions();
    
    // 查询方法
    bool HasOpenPositions();
    int GetOpenPositionCount();
    SPositionInfo GetPositionInfo(int index);
    
    // 风险控制方法
    bool IsMaxPositionsReached();
    double GetTotalExposure();
    
    // 调试方法
    void PrintPositionStatus();
};

//+------------------------------------------------------------------+
//| 构造函数                                                         |
//+------------------------------------------------------------------+
CDayTradePositionManager::CDayTradePositionManager()
{
    m_magicNumber = 0;
    m_maxPositionTime = 60;
    m_tradeComment = "DayTrade";
    ArrayResize(m_positions, 0);
}

//+------------------------------------------------------------------+
//| 析构函数                                                         |
//+------------------------------------------------------------------+
CDayTradePositionManager::~CDayTradePositionManager()
{
    ArrayFree(m_positions);
}

//+------------------------------------------------------------------+
//| 初始化方法                                                       |
//+------------------------------------------------------------------+
bool CDayTradePositionManager::Initialize(int magicNumber, int maxPositionTime)
{
    m_magicNumber = magicNumber;
    m_maxPositionTime = maxPositionTime;
    m_tradeComment = InpTradeComment;
    
    // 设置交易参数
    m_trade.SetExpertMagicNumber(m_magicNumber);
    m_trade.SetMarginMode();
    m_trade.SetTypeFillingBySymbol(Symbol());
    
    Print("日内交易仓位管理器初始化成功");
    Print("魔术号: ", m_magicNumber);
    Print("最大持仓时间: ", m_maxPositionTime, " 分钟");
    
    return true;
}

//+------------------------------------------------------------------+
//| 开仓方法                                                         |
//+------------------------------------------------------------------+
bool CDayTradePositionManager::OpenPosition(string symbol, ENUM_ORDER_TYPE orderType, double lotSize, 
                                           double stopLoss, double takeProfit)
{
    // 检查是否已达到最大持仓数
    if(IsMaxPositionsReached())
    {
        Print("已达到最大持仓数，无法开新仓");
        return false;
    }
    
    // 获取当前价格
    double price = 0;
    if(orderType == ORDER_TYPE_BUY)
    {
        price = SymbolInfoDouble(symbol, SYMBOL_ASK);
    }
    else if(orderType == ORDER_TYPE_SELL)
    {
        price = SymbolInfoDouble(symbol, SYMBOL_BID);
    }
    
    if(price <= 0)
    {
        Print("获取价格失败");
        return false;
    }
    
    // 执行开仓
    bool result = false;
    if(orderType == ORDER_TYPE_BUY)
    {
        result = m_trade.Buy(lotSize, symbol, price, stopLoss, takeProfit, m_tradeComment);
    }
    else if(orderType == ORDER_TYPE_SELL)
    {
        result = m_trade.Sell(lotSize, symbol, price, stopLoss, takeProfit, m_tradeComment);
    }
    
    if(result)
    {
        ulong ticket = m_trade.ResultOrder();
        Print("✅ 开仓成功 - 订单号: ", ticket, " 品种: ", symbol, " 手数: ", lotSize);
        
        // 更新持仓信息
        UpdatePositionInfo();
        
        return true;
    }
    else
    {
        Print("❌ 开仓失败 - 错误: ", m_trade.ResultRetcode(), " 描述: ", m_trade.ResultRetcodeDescription());
        return false;
    }
}

//+------------------------------------------------------------------+
//| 平仓所有持仓                                                     |
//+------------------------------------------------------------------+
bool CDayTradePositionManager::CloseAllPositions(string reason)
{
    UpdatePositionInfo();
    
    bool allClosed = true;
    int positionCount = ArraySize(m_positions);
    
    for(int i = 0; i < positionCount; i++)
    {
        if(!ClosePosition(m_positions[i].ticket, reason))
        {
            allClosed = false;
        }
    }
    
    if(allClosed && positionCount > 0)
    {
        Print("✅ 所有持仓已平仓 - 原因: ", reason);
        ArrayResize(m_positions, 0);
    }
    
    return allClosed;
}

//+------------------------------------------------------------------+
//| 管理现有持仓                                                     |
//+------------------------------------------------------------------+
bool CDayTradePositionManager::ManageExistingPositions()
{
    UpdatePositionInfo();
    
    int positionCount = ArraySize(m_positions);
    if(positionCount == 0) return true;
    
    // 检查每个持仓
    for(int i = positionCount - 1; i >= 0; i--)
    {
        SPositionInfo pos = m_positions[i];
        
        // 检查是否超时
        if(IsPositionExpired(pos))
        {
            ClosePosition(pos.ticket, "持仓超时");
            continue;
        }
        
        // 这里可以添加其他管理逻辑，比如：
        // - 移动止损
        // - 部分平仓
        // - 根据市场条件调整止盈止损等
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 更新持仓信息                                                     |
//+------------------------------------------------------------------+
bool CDayTradePositionManager::UpdatePositionInfo()
{
    ArrayResize(m_positions, 0);
    
    int totalPositions = PositionsTotal();
    int validPositions = 0;
    
    for(int i = 0; i < totalPositions; i++)
    {
        ulong ticket = PositionGetTicket(i);
        if(ticket <= 0) continue;
        
        // 检查是否是本EA的持仓
        if(PositionGetInteger(POSITION_MAGIC) != m_magicNumber)
            continue;
        
        // 添加到持仓数组
        ArrayResize(m_positions, validPositions + 1);
        
        m_positions[validPositions].ticket = ticket;
        m_positions[validPositions].openTime = (datetime)PositionGetInteger(POSITION_TIME);
        m_positions[validPositions].openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
        m_positions[validPositions].lotSize = PositionGetDouble(POSITION_VOLUME);
        m_positions[validPositions].stopLoss = PositionGetDouble(POSITION_SL);
        m_positions[validPositions].takeProfit = PositionGetDouble(POSITION_TP);
        m_positions[validPositions].type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
        m_positions[validPositions].symbol = PositionGetString(POSITION_SYMBOL);
        
        validPositions++;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 检查持仓是否过期                                                 |
//+------------------------------------------------------------------+
bool CDayTradePositionManager::IsPositionExpired(const SPositionInfo& pos)
{
    if(m_maxPositionTime <= 0) return false;
    
    datetime currentTime = TimeCurrent();
    int elapsedMinutes = (int)((currentTime - pos.openTime) / 60);
    
    return (elapsedMinutes >= m_maxPositionTime);
}

//+------------------------------------------------------------------+
//| 平仓指定持仓                                                     |
//+------------------------------------------------------------------+
bool CDayTradePositionManager::ClosePosition(ulong ticket, string reason)
{
    if(!PositionSelectByTicket(ticket))
    {
        Print("选择持仓失败 - 订单号: ", ticket);
        return false;
    }
    
    string symbol = PositionGetString(POSITION_SYMBOL);
    double lotSize = PositionGetDouble(POSITION_VOLUME);
    
    bool result = m_trade.PositionClose(ticket);
    
    if(result)
    {
        Print("✅ 平仓成功 - 订单号: ", ticket, " 原因: ", reason);
        return true;
    }
    else
    {
        Print("❌ 平仓失败 - 订单号: ", ticket, " 错误: ", m_trade.ResultRetcode());
        return false;
    }
}

//+------------------------------------------------------------------+
//| 检查是否有开仓                                                   |
//+------------------------------------------------------------------+
bool CDayTradePositionManager::HasOpenPositions()
{
    UpdatePositionInfo();
    return (ArraySize(m_positions) > 0);
}

//+------------------------------------------------------------------+
//| 获取开仓数量                                                     |
//+------------------------------------------------------------------+
int CDayTradePositionManager::GetOpenPositionCount()
{
    UpdatePositionInfo();
    return ArraySize(m_positions);
}

//+------------------------------------------------------------------+
//| 获取持仓信息                                                     |
//+------------------------------------------------------------------+
SPositionInfo CDayTradePositionManager::GetPositionInfo(int index)
{
    SPositionInfo emptyPos = {};
    
    if(index < 0 || index >= ArraySize(m_positions))
        return emptyPos;
    
    return m_positions[index];
}

//+------------------------------------------------------------------+
//| 检查是否达到最大持仓数                                           |
//+------------------------------------------------------------------+
bool CDayTradePositionManager::IsMaxPositionsReached()
{
    // 日内交易通常只持有一个仓位
    return (GetOpenPositionCount() >= 1);
}

//+------------------------------------------------------------------+
//| 获取总敞口                                                       |
//+------------------------------------------------------------------+
double CDayTradePositionManager::GetTotalExposure()
{
    UpdatePositionInfo();
    
    double totalExposure = 0;
    int positionCount = ArraySize(m_positions);
    
    for(int i = 0; i < positionCount; i++)
    {
        totalExposure += m_positions[i].lotSize;
    }
    
    return totalExposure;
}

//+------------------------------------------------------------------+
//| 打印持仓状态                                                     |
//+------------------------------------------------------------------+
void CDayTradePositionManager::PrintPositionStatus()
{
    UpdatePositionInfo();
    
    int positionCount = ArraySize(m_positions);
    
    Print("=== 持仓状态 ===");
    Print("持仓数量: ", positionCount);
    
    for(int i = 0; i < positionCount; i++)
    {
        SPositionInfo pos = m_positions[i];
        int elapsedMinutes = (int)((TimeCurrent() - pos.openTime) / 60);
        
        Print("持仓 ", i+1, ":");
        Print("  订单号: ", pos.ticket);
        Print("  品种: ", pos.symbol);
        Print("  类型: ", pos.type == POSITION_TYPE_BUY ? "买入" : "卖出");
        Print("  手数: ", pos.lotSize);
        Print("  开仓价: ", pos.openPrice);
        Print("  止损: ", pos.stopLoss);
        Print("  止盈: ", pos.takeProfit);
        Print("  持仓时间: ", elapsedMinutes, " 分钟");
    }
    
    Print("总敞口: ", GetTotalExposure());
    Print("===============");
}

#endif // DAY_TRADE_POSITION_MANAGER_H
