//+------------------------------------------------------------------+
//|                                            TestCompilation.mq5 |
//|                                  Copyright 2024, TradingSystem |
//|                                             编译测试脚本        |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, TradingSystem"
#property link      ""
#property version   "1.00"
#property script_show_inputs

// 测试包含所有模块
#include "MarketStateAnalyzer.mqh"
#include "EntrySignalModule.mqh"
#include "ExitManager.mqh"
#include "PositionManager.mqh"

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== 编译测试开始 ===");
    
    // 测试创建各个模块实例
    CMarketStateAnalyzer* marketAnalyzer = new CMarketStateAnalyzer();
    CEntrySignalModule* entryModule = new CEntrySignalModule();
    CPositionManager* positionManager = new CPositionManager();
    
    if(marketAnalyzer != NULL && entryModule != NULL && positionManager != NULL)
    {
        Print("✓ 所有模块实例创建成功");
        
        // 测试初始化（使用默认参数）
        bool marketInit = marketAnalyzer.Init(14, 60, 1.5, 14, 25.0, 3, 15, 30, 60, true, true, true);
        bool entryInit = entryModule.Init(20, 20, 2.0, 14, true, true, true);
        bool positionInit = positionManager.Init(1.0, 15.0, 0.5, 1.5, 3, 3, 888888, "Test");
        
        if(marketInit && entryInit && positionInit)
        {
            Print("✓ 所有模块初始化成功");
            Print("✓ 新增功能验证：");
            Print("  - ATR启用状态：", true);
            Print("  - ADX启用状态：", true);
            Print("  - GMMA启用状态：", true);
            Print("  - 唐奇安突破启用：", true);
            Print("  - EMA突破启用：", true);
            Print("  - ATR过滤启用：", true);
            Print("  - 单一品种最大持仓：", 3, "笔");
            Print("  - 最大加仓层级：", 3, "层");
        }
        else
        {
            Print("✗ 模块初始化失败");
        }
    }
    else
    {
        Print("✗ 模块实例创建失败");
    }
    
    // 清理资源
    if(marketAnalyzer != NULL) delete marketAnalyzer;
    if(entryModule != NULL) delete entryModule;
    if(positionManager != NULL) delete positionManager;
    
    Print("=== 编译测试完成 ===");
}