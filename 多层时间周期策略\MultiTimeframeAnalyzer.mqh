//+------------------------------------------------------------------+
//|                                      MultiTimeframeAnalyzer.mqh |
//|                                  Copyright 2024, TradingSystem |
//|                                    多时间周期分析器模块         |
//+------------------------------------------------------------------+

#ifndef MULTI_TIMEFRAME_ANALYZER_H
#define MULTI_TIMEFRAME_ANALYZER_H

// 信号类型枚举
enum ENUM_SIGNAL_TYPE
{
    SIGNAL_NONE = 0,    // 无信号
    SIGNAL_BUY = 1,     // 买入信号
    SIGNAL_SELL = -1    // 卖出信号
};

// 趋势状态枚举
enum ENUM_TREND_STATE
{
    TREND_UNKNOWN = 0,  // 未知
    TREND_UP = 1,       // 上升趋势
    TREND_DOWN = -1,    // 下降趋势
    TREND_SIDEWAYS = 2  // 横盘
};

//+------------------------------------------------------------------+
//| 多时间周期分析器类                                               |
//+------------------------------------------------------------------+
class CMultiTimeframeAnalyzer
{
private:
    // 时间周期设置
    ENUM_TIMEFRAMES m_anchorTF;     // 锚定时间周期 (1H)
    ENUM_TIMEFRAMES m_setupTF;      // 设伏时间周期 (15M)
    ENUM_TIMEFRAMES m_triggerTF;    // 触发时间周期 (5M)
    
    // EMA指标句柄
    int m_anchorEMA;                // 锚定周期EMA句柄
    int m_setupEMA;                 // 设伏周期EMA句柄
    int m_triggerEMA;               // 触发周期EMA句柄
    
    // ATR指标句柄 (用于波动率分析)
    int m_anchorATR;                // 锚定周期ATR句柄
    int m_setupATR;                 // 设伏周期ATR句柄
    
    // 分析结果缓存
    ENUM_TREND_STATE m_anchorTrend; // 锚定周期趋势状态
    bool m_setupPullback;           // 设伏周期是否在回调
    bool m_setupRecovery;           // 设伏周期是否显示恢复
    
    // 内部分析方法
    bool AnalyzeAnchorTrend(string symbol);
    bool AnalyzeSetupPullback(string symbol);
    bool AnalyzeSetupRecovery(string symbol);
    
    // 辅助方法
    double GetEMAValue(int handle, int shift);
    double GetATRValue(int handle, int shift);
    bool IsEMATrendUp(int handle, int bars);
    double CalculatePullbackPercent(string symbol, ENUM_TIMEFRAMES tf);

public:
    // 构造函数和析构函数
    CMultiTimeframeAnalyzer();
    ~CMultiTimeframeAnalyzer();
    
    // 初始化方法
    bool Initialize(ENUM_TIMEFRAMES anchorTF, ENUM_TIMEFRAMES setupTF, ENUM_TIMEFRAMES triggerTF);
    
    // 主要分析方法
    bool UpdateAnalysis(string symbol);
    
    // 查询方法 - 对应交易手册的Checklist
    bool IsAnchorTrendBullish();                    // A1. 1小时图趋势是否明确向上
    bool IsSetupInHealthyPullback();                // B1. 15分钟图是否处于健康回调
    bool IsSetupShowingRecovery();                  // B2. 15分钟图是否显示回调结束
    
    // 获取分析结果
    ENUM_TREND_STATE GetAnchorTrendState() { return m_anchorTrend; }
    
    // 调试信息
    void PrintAnalysisStatus(string symbol);
};

//+------------------------------------------------------------------+
//| 构造函数                                                         |
//+------------------------------------------------------------------+
CMultiTimeframeAnalyzer::CMultiTimeframeAnalyzer()
{
    m_anchorTF = PERIOD_H1;
    m_setupTF = PERIOD_M15;
    m_triggerTF = PERIOD_M5;
    
    m_anchorEMA = INVALID_HANDLE;
    m_setupEMA = INVALID_HANDLE;
    m_triggerEMA = INVALID_HANDLE;
    m_anchorATR = INVALID_HANDLE;
    m_setupATR = INVALID_HANDLE;
    
    m_anchorTrend = TREND_UNKNOWN;
    m_setupPullback = false;
    m_setupRecovery = false;
}

//+------------------------------------------------------------------+
//| 析构函数                                                         |
//+------------------------------------------------------------------+
CMultiTimeframeAnalyzer::~CMultiTimeframeAnalyzer()
{
    // 释放指标句柄
    if(m_anchorEMA != INVALID_HANDLE) IndicatorRelease(m_anchorEMA);
    if(m_setupEMA != INVALID_HANDLE) IndicatorRelease(m_setupEMA);
    if(m_triggerEMA != INVALID_HANDLE) IndicatorRelease(m_triggerEMA);
    if(m_anchorATR != INVALID_HANDLE) IndicatorRelease(m_anchorATR);
    if(m_setupATR != INVALID_HANDLE) IndicatorRelease(m_setupATR);
}

//+------------------------------------------------------------------+
//| 初始化方法                                                       |
//+------------------------------------------------------------------+
bool CMultiTimeframeAnalyzer::Initialize(ENUM_TIMEFRAMES anchorTF, ENUM_TIMEFRAMES setupTF, ENUM_TIMEFRAMES triggerTF)
{
    m_anchorTF = anchorTF;
    m_setupTF = setupTF;
    m_triggerTF = triggerTF;
    
    string symbol = Symbol();
    
    // 创建EMA指标句柄
    m_anchorEMA = iMA(symbol, m_anchorTF, InpEMA_Period, 0, MODE_EMA, PRICE_CLOSE);
    m_setupEMA = iMA(symbol, m_setupTF, InpEMA_Period, 0, MODE_EMA, PRICE_CLOSE);
    m_triggerEMA = iMA(symbol, m_triggerTF, InpEMA_Period, 0, MODE_EMA, PRICE_CLOSE);
    
    // 创建ATR指标句柄
    m_anchorATR = iATR(symbol, m_anchorTF, 14);
    m_setupATR = iATR(symbol, m_setupTF, 14);
    
    // 验证句柄有效性
    if(m_anchorEMA == INVALID_HANDLE || m_setupEMA == INVALID_HANDLE || 
       m_triggerEMA == INVALID_HANDLE || m_anchorATR == INVALID_HANDLE || 
       m_setupATR == INVALID_HANDLE)
    {
        Print("多时间周期分析器: 指标句柄创建失败");
        return false;
    }
    
    Print("多时间周期分析器初始化成功");
    Print("锚定周期: ", EnumToString(m_anchorTF));
    Print("设伏周期: ", EnumToString(m_setupTF));
    Print("触发周期: ", EnumToString(m_triggerTF));
    
    return true;
}

//+------------------------------------------------------------------+
//| 更新分析                                                         |
//+------------------------------------------------------------------+
bool CMultiTimeframeAnalyzer::UpdateAnalysis(string symbol)
{
    // 分析锚定时间周期趋势
    if(!AnalyzeAnchorTrend(symbol))
        return false;
    
    // 分析设伏时间周期回调
    if(!AnalyzeSetupPullback(symbol))
        return false;
    
    // 分析设伏时间周期恢复
    if(!AnalyzeSetupRecovery(symbol))
        return false;
    
    return true;
}

//+------------------------------------------------------------------+
//| 分析锚定时间周期趋势 (1小时图)                                   |
//+------------------------------------------------------------------+
bool CMultiTimeframeAnalyzer::AnalyzeAnchorTrend(string symbol)
{
    // 获取当前价格和EMA值
    double currentPrice = iClose(symbol, m_anchorTF, 0);
    double emaValue = GetEMAValue(m_anchorEMA, 0);
    
    if(currentPrice == 0 || emaValue == 0)
        return false;
    
    // 检查价格是否在EMA之上
    bool priceAboveEMA = currentPrice > emaValue;
    
    // 检查EMA是否呈上升趋势
    bool emaTrendUp = IsEMATrendUp(m_anchorEMA, InpMinTrendBars);
    
    // 综合判断趋势状态
    if(priceAboveEMA && emaTrendUp)
    {
        m_anchorTrend = TREND_UP;
    }
    else if(!priceAboveEMA && !emaTrendUp)
    {
        m_anchorTrend = TREND_DOWN;
    }
    else
    {
        m_anchorTrend = TREND_SIDEWAYS;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 分析设伏时间周期回调 (15分钟图)                                  |
//+------------------------------------------------------------------+
bool CMultiTimeframeAnalyzer::AnalyzeSetupPullback(string symbol)
{
    // 获取当前价格和EMA值
    double currentPrice = iClose(symbol, m_setupTF, 0);
    double emaValue = GetEMAValue(m_setupEMA, 0);
    
    if(currentPrice == 0 || emaValue == 0)
        return false;
    
    // 计算回调百分比
    double pullbackPercent = CalculatePullbackPercent(symbol, m_setupTF);
    
    // 判断是否为健康回调
    // 1. 价格接近EMA20附近 (允许一定偏差)
    double priceEMADistance = MathAbs(currentPrice - emaValue) / emaValue;
    bool nearEMA = priceEMADistance <= 0.02; // 2%以内认为接近
    
    // 2. 回调幅度在合理范围内
    bool reasonablePullback = pullbackPercent > 0.1 && pullbackPercent <= InpPullbackMaxPercent;
    
    m_setupPullback = nearEMA || reasonablePullback;
    
    return true;
}

//+------------------------------------------------------------------+
//| 分析设伏时间周期恢复 (15分钟图)                                  |
//+------------------------------------------------------------------+
bool CMultiTimeframeAnalyzer::AnalyzeSetupRecovery(string symbol)
{
    // 检查最近几根K线是否显示止跌企稳
    bool hasStabilization = false;
    
    // 获取最近3根K线的收盘价
    double close0 = iClose(symbol, m_setupTF, 0);
    double close1 = iClose(symbol, m_setupTF, 1);
    double close2 = iClose(symbol, m_setupTF, 2);
    
    // 获取最近3根K线的最低价
    double low0 = iLow(symbol, m_setupTF, 0);
    double low1 = iLow(symbol, m_setupTF, 1);
    double low2 = iLow(symbol, m_setupTF, 2);
    
    if(close0 > 0 && close1 > 0 && close2 > 0)
    {
        // 检查是否形成小平台 (最近3根K线收盘价相对稳定)
        double maxClose = MathMax(MathMax(close0, close1), close2);
        double minClose = MathMin(MathMin(close0, close1), close2);
        double priceRange = (maxClose - minClose) / minClose;
        
        bool formingPlatform = priceRange <= 0.01; // 1%以内认为形成平台
        
        // 检查是否止跌 (最低价不再创新低)
        bool stoppedFalling = low0 >= low1 && low1 >= low2;
        
        hasStabilization = formingPlatform || stoppedFalling;
    }
    
    m_setupRecovery = hasStabilization;

    return true;
}

//+------------------------------------------------------------------+
//| 获取EMA指标值                                                    |
//+------------------------------------------------------------------+
double CMultiTimeframeAnalyzer::GetEMAValue(int handle, int shift)
{
    double buffer[1];
    if(CopyBuffer(handle, 0, shift, 1, buffer) <= 0)
        return 0;
    return buffer[0];
}

//+------------------------------------------------------------------+
//| 获取ATR指标值                                                    |
//+------------------------------------------------------------------+
double CMultiTimeframeAnalyzer::GetATRValue(int handle, int shift)
{
    double buffer[1];
    if(CopyBuffer(handle, 0, shift, 1, buffer) <= 0)
        return 0;
    return buffer[0];
}

//+------------------------------------------------------------------+
//| 检查EMA是否呈上升趋势                                            |
//+------------------------------------------------------------------+
bool CMultiTimeframeAnalyzer::IsEMATrendUp(int handle, int bars)
{
    if(bars < 2) bars = 2;

    double emaValues[];
    ArraySetAsSeries(emaValues, true);

    if(CopyBuffer(handle, 0, 0, bars, emaValues) <= 0)
        return false;

    // 检查EMA是否连续上升
    int upCount = 0;
    for(int i = 1; i < bars; i++)
    {
        if(emaValues[i-1] > emaValues[i])
            upCount++;
    }

    // 如果大部分K线都是上升的，认为趋势向上
    return (upCount >= bars * InpTrendStrengthThreshold);
}

//+------------------------------------------------------------------+
//| 计算回调百分比                                                   |
//+------------------------------------------------------------------+
double CMultiTimeframeAnalyzer::CalculatePullbackPercent(string symbol, ENUM_TIMEFRAMES tf)
{
    // 获取最近20根K线的最高价和最低价
    int lookback = 20;
    double highs[], lows[];
    ArraySetAsSeries(highs, true);
    ArraySetAsSeries(lows, true);

    if(CopyHigh(symbol, tf, 0, lookback, highs) <= 0 ||
       CopyLow(symbol, tf, 0, lookback, lows) <= 0)
        return 0;

    // 找到最高点和最低点
    double highestHigh = highs[ArrayMaximum(highs)];
    double lowestLow = lows[ArrayMinimum(lows)];
    double currentPrice = iClose(symbol, tf, 0);

    if(highestHigh <= lowestLow || currentPrice <= 0)
        return 0;

    // 计算从最高点的回调百分比
    double pullbackPercent = (highestHigh - currentPrice) / (highestHigh - lowestLow);

    return MathMax(0, MathMin(1, pullbackPercent));
}

//+------------------------------------------------------------------+
//| 查询方法 - A1. 1小时图趋势是否明确向上                          |
//+------------------------------------------------------------------+
bool CMultiTimeframeAnalyzer::IsAnchorTrendBullish()
{
    return (m_anchorTrend == TREND_UP);
}

//+------------------------------------------------------------------+
//| 查询方法 - B1. 15分钟图是否处于健康回调                         |
//+------------------------------------------------------------------+
bool CMultiTimeframeAnalyzer::IsSetupInHealthyPullback()
{
    return m_setupPullback;
}

//+------------------------------------------------------------------+
//| 查询方法 - B2. 15分钟图是否显示回调结束                         |
//+------------------------------------------------------------------+
bool CMultiTimeframeAnalyzer::IsSetupShowingRecovery()
{
    return m_setupRecovery;
}

//+------------------------------------------------------------------+
//| 打印分析状态 (调试用)                                            |
//+------------------------------------------------------------------+
void CMultiTimeframeAnalyzer::PrintAnalysisStatus(string symbol)
{
    Print("=== 多时间周期分析状态 ===");
    Print("品种: ", symbol);
    Print("锚定趋势 (", EnumToString(m_anchorTF), "): ",
          m_anchorTrend == TREND_UP ? "上升" :
          m_anchorTrend == TREND_DOWN ? "下降" :
          m_anchorTrend == TREND_SIDEWAYS ? "横盘" : "未知");
    Print("设伏回调 (", EnumToString(m_setupTF), "): ", m_setupPullback ? "是" : "否");
    Print("设伏恢复 (", EnumToString(m_setupTF), "): ", m_setupRecovery ? "是" : "否");
    Print("========================");
}

#endif // MULTI_TIMEFRAME_ANALYZER_H
