//+------------------------------------------------------------------+
//|                                        ProductionPatternEA.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"

#include "PatternDetector.mqh"
#include "RiskManager.mqh"

// 输入参数
input double RiskPerTrade = 2.0;        // 每笔交易风险百分比
input double MaxTotalRisk = 6.0;        // 最大总风险百分比
input int ATRPeriod = 14;               // ATR周期
input double ATRMultiplier = 2.0;       // ATR止损倍数
input double RiskRewardRatio = 2.0;     // 风险收益比
input int MaxPositions = 3;             // 最大持仓数
input int MagicNumber = 123456;         // 魔术数字
input bool UseTimeFilter = true;        // 使用时间过滤
input int StartHour = 8;                // 开始交易时间
input int EndHour = 22;                 // 结束交易时间
input int MinBarsRequired = 100;        // 最少K线数量

// 全局变量
CPatternDetector* g_patternDetector;
CRiskManager* g_riskManager;
int g_atrHandle;
double g_winLossMultiplier = 1.0;       // 盈冲输缩倍数
int g_recentTrades[5];                  // 最近5笔交易结果 (1=盈利, 0=亏损)
int g_tradeIndex = 0;                   // 交易索引

// 函数声明
bool CanOpenNewPosition();
double CalculatePositionSize(double entry_price, double stop_loss);
void UpdateWinLossMultiplier();
void ManagePositions();

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // 初始化模式分析器
   g_patternDetector = new CPatternDetector();
   if(g_patternDetector == NULL)
   {
      Print("模式分析器初始化失败");
      return INIT_FAILED;
   }
   
   // 初始化风险管理器
   g_riskManager = new CRiskManager(RiskPerTrade, MaxTotalRisk, MaxPositions);
   if(g_riskManager == NULL)
   {
      Print("风险管理器初始化失败");
      delete g_patternDetector;
      return INIT_FAILED;
   }
   
   // 设置风险管理参数
   g_riskManager.m_riskPerTrade = RiskPerTrade;
   g_riskManager.m_maxTotalRisk = MaxTotalRisk;
   g_riskManager.m_atrMultiplier = ATRMultiplier;
   g_riskManager.m_riskRewardRatio = RiskRewardRatio;
   g_riskManager.m_maxPositions = MaxPositions;
   
   // 初始化ATR指标
   g_atrHandle = iATR(_Symbol, _Period, ATRPeriod);
   if(g_atrHandle == INVALID_HANDLE)
   {
      Print("ATR指标初始化失败");
      delete g_patternDetector;
      delete g_riskManager;
      return INIT_FAILED;
   }
   
   // 初始化交易历史
   ArrayInitialize(g_recentTrades, -1);
   
   Print("生产级模式分析EA初始化成功");
   Print("风险参数: 每笔", RiskPerTrade, "%, 最大总风险", MaxTotalRisk, "%");
   Print("ATR参数: 周期", ATRPeriod, ", 倍数", ATRMultiplier);
   
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // 清理资源
   if(g_patternDetector != NULL)
   {
      delete g_patternDetector;
      g_patternDetector = NULL;
   }
   
   if(g_riskManager != NULL)
   {
      delete g_riskManager;
      g_riskManager = NULL;
   }
   
   if(g_atrHandle != INVALID_HANDLE)
   {
      IndicatorRelease(g_atrHandle);
   }
   
   Print("生产级模式分析EA已卸载");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // 检查基本条件
   if(!IsNewBar()) return;
   if(!IsTimeToTrade()) return;
   if(Bars(_Symbol, _Period) < MinBarsRequired) return;
   
   // 更新盈冲输缩倍数
   UpdateWinLossMultiplier();
   
   // 检查是否可以开新仓
   if(!CanOpenNewPosition()) return;
   
   // 获取市场数据
   int data_size = MathMin(200, Bars(_Symbol, _Period));
   double open[], high[], low[], close[];
   long volume[];
   
   if(!GetMarketData(open, high, low, close, volume, data_size)) return;
   
   // 分析交易信号
   string signal = g_patternDetector.DetectTradingSignals(open, high, low, close, volume, data_size);
   
   if(signal != "无交易信号")
   {
      Print("检测到交易信号:");
      Print(signal);
      
      // 执行交易
      ExecuteTrade(signal, open, high, low, close, volume, data_size);
   }
   
   // 管理现有持仓
   ManagePositions();
}

//+------------------------------------------------------------------+
//| 检查是否为新K线                                                   |
//+------------------------------------------------------------------+
bool IsNewBar()
{
   static datetime last_time = 0;
   datetime current_time = iTime(_Symbol, _Period, 0);
   
   if(current_time != last_time)
   {
      last_time = current_time;
      return true;
   }
   return false;
}

//+------------------------------------------------------------------+
//| 检查是否在交易时间内                                               |
//+------------------------------------------------------------------+
bool IsTimeToTrade()
{
   if(!UseTimeFilter) return true;
   
   MqlDateTime dt;
   TimeToStruct(TimeCurrent(), dt);
   
   return (dt.hour >= StartHour && dt.hour < EndHour);
}

//+------------------------------------------------------------------+
//| 获取市场数据                                                       |
//+------------------------------------------------------------------+
bool GetMarketData(double &open[], double &high[], double &low[], double &close[], long &volume[], int size)
{
   ArraySetAsSeries(open, true);
   ArraySetAsSeries(high, true);
   ArraySetAsSeries(low, true);
   ArraySetAsSeries(close, true);
   ArraySetAsSeries(volume, true);
   
   if(CopyOpen(_Symbol, _Period, 0, size, open) != size) return false;
   if(CopyHigh(_Symbol, _Period, 0, size, high) != size) return false;
   if(CopyLow(_Symbol, _Period, 0, size, low) != size) return false;
   if(CopyClose(_Symbol, _Period, 0, size, close) != size) return false;
   if(CopyTickVolume(_Symbol, _Period, 0, size, volume) != size) return false;
   
   // 反转数组顺序以匹配分析器期望的格式
   ArrayReverse(open);
   ArrayReverse(high);
   ArrayReverse(low);
   ArrayReverse(close);
   ArrayReverse(volume);
   
   return true;
}

//+------------------------------------------------------------------+
//| 执行交易                                                          |
//+------------------------------------------------------------------+
void ExecuteTrade(string signal, const double &open[], const double &high[], const double &low[], const double &close[], const long &volume[], int size)
{
   // 获取ATR值
   double atr[];
   ArraySetAsSeries(atr, true);
   if(CopyBuffer(g_atrHandle, 0, 0, 1, atr) != 1) return;
   
   double current_price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   double atr_value = atr[0];
   
   // 计算止损和止盈
   double stop_loss = current_price - atr_value * ATRMultiplier;
   double take_profit = current_price + (current_price - stop_loss) * RiskRewardRatio;
   
   // 计算手数（考虑盈冲输缩）
   double base_lot_size = CalculatePositionSize(current_price, stop_loss);
   double adjusted_lot_size = base_lot_size * g_winLossMultiplier;
   
   // 限制手数范围
   double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
   double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
   adjusted_lot_size = MathMax(min_lot, MathMin(max_lot, adjusted_lot_size));
   
   // 执行买入订单
   MqlTradeRequest request = {};
   MqlTradeResult result = {};
   
   request.action = TRADE_ACTION_DEAL;
   request.symbol = _Symbol;
   request.volume = adjusted_lot_size;
   request.type = ORDER_TYPE_BUY;
   request.price = current_price;
   request.sl = stop_loss;
   request.tp = take_profit;
   request.magic = MagicNumber;
   request.comment = StringFormat("Pattern_%s_%.2f", StringSubstr(signal, 0, 1), g_winLossMultiplier);
   
   if(OrderSend(request, result))
   {
      Print(StringFormat("交易执行成功: 手数=%.2f, 入场=%.5f, 止损=%.5f, 止盈=%.5f, 倍数=%.2f", 
                        adjusted_lot_size, current_price, stop_loss, take_profit, g_winLossMultiplier));
   }
   else
   {
      Print("交易执行失败: ", result.comment);
   }
}

//+------------------------------------------------------------------+
//| 管理现有持仓                                                       |
//+------------------------------------------------------------------+
void ManagePositions()
{
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(PositionGetSymbol(i) != _Symbol) continue;
      if(PositionGetInteger(POSITION_MAGIC) != MagicNumber) continue;
      
      ulong ticket = PositionGetInteger(POSITION_TICKET);
      double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
      double current_price = PositionGetDouble(POSITION_PRICE_CURRENT);
      double stop_loss = PositionGetDouble(POSITION_SL);
      
      // 移动止损逻辑
      if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
      {
         double atr[];
         ArraySetAsSeries(atr, true);
         if(CopyBuffer(g_atrHandle, 0, 0, 1, atr) == 1)
         {
            double new_sl = current_price - atr[0] * ATRMultiplier;
            
            // 只有当新止损更有利时才移动
            if(new_sl > stop_loss && new_sl < current_price)
            {
               MqlTradeRequest request = {};
               MqlTradeResult result = {};
               
               request.action = TRADE_ACTION_SLTP;
               request.position = ticket;
               request.sl = new_sl;
               request.tp = PositionGetDouble(POSITION_TP);
               
               if(OrderSend(request, result))
               {
                  Print(StringFormat("移动止损成功: 票号=%d, 新止损=%.5f", ticket, new_sl));
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 检查是否可以开新仓                                                 |
//+------------------------------------------------------------------+
bool CanOpenNewPosition()
{
   // 检查最大持仓数
   int current_positions = 0;
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == MagicNumber)
         current_positions++;
   }
   
   if(current_positions >= MaxPositions)
      return false;
   
   // 检查总风险
   double total_risk = 0.0;
   double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == MagicNumber)
      {
         double position_volume = PositionGetDouble(POSITION_VOLUME);
         double position_sl = PositionGetDouble(POSITION_SL);
         double position_open = PositionGetDouble(POSITION_PRICE_OPEN);
         
         if(position_sl > 0)
         {
            double risk_amount = MathAbs(position_open - position_sl) * position_volume * SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
            total_risk += (risk_amount / account_balance) * 100.0;
         }
      }
   }
   
   return (total_risk + RiskPerTrade <= MaxTotalRisk);
}

//+------------------------------------------------------------------+
//| 计算仓位大小                                                       |
//+------------------------------------------------------------------+
double CalculatePositionSize(double entry_price, double stop_loss)
{
   if(stop_loss <= 0 || entry_price <= 0)
      return 0.0;
   
   double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   double risk_amount = account_balance * (RiskPerTrade / 100.0);
   
   double price_diff = MathAbs(entry_price - stop_loss);
   double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
   double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
   
   if(tick_size <= 0 || tick_value <= 0)
      return 0.0;
   
   double points_diff = price_diff / tick_size;
   double risk_per_lot = points_diff * tick_value;
   
   if(risk_per_lot <= 0)
      return 0.0;
   
   double lot_size = risk_amount / risk_per_lot;
   
   // 限制在允许范围内
   double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
   double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
   double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
   
   lot_size = MathMax(min_lot, MathMin(max_lot, lot_size));
   
   // 调整到步长
   if(lot_step > 0)
      lot_size = MathFloor(lot_size / lot_step) * lot_step;
   
   return lot_size;
}

//+------------------------------------------------------------------+
//| 更新盈冲输缩倍数                                                   |
//+------------------------------------------------------------------+
void UpdateWinLossMultiplier()
{
   // 计算最近5笔交易的胜率
   int win_count = 0;
   int total_trades = 0;
   
   for(int i = 0; i < 5; i++)
   {
      if(g_recentTrades[i] != -1)
      {
         total_trades++;
         if(g_recentTrades[i] == 1) win_count++;
      }
   }
   
   if(total_trades >= 3)  // 至少3笔交易才调整
   {
      double win_rate = (double)win_count / total_trades;
      
      if(win_rate > 0.6)  // 胜率>60%
      {
         g_winLossMultiplier = MathMin(g_winLossMultiplier * 1.2, 2.0);
      }
      else if(win_rate < 0.4)  // 胜率<40%
      {
         g_winLossMultiplier = MathMax(g_winLossMultiplier * 0.8, 0.5);
      }
   }
}

//+------------------------------------------------------------------+
//| 交易事件处理                                                       |
//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction& trans,
                       const MqlTradeRequest& request,
                       const MqlTradeResult& result)
{
   // 只处理我们的订单
   if(trans.symbol != _Symbol) return;
   if(request.magic != MagicNumber) return;
   
   // 记录交易结果
   if(trans.type == TRADE_TRANSACTION_DEAL_ADD)
   {
      if(trans.deal_type == DEAL_TYPE_BUY || trans.deal_type == DEAL_TYPE_SELL)
      {
         // 开仓交易
         Print(StringFormat("开仓: 票号=%d, 价格=%.5f, 手数=%.2f", 
                           trans.deal, trans.price, trans.volume));
      }
   }
   else if(trans.type == TRADE_TRANSACTION_HISTORY_ADD)
   {
      // 平仓交易
      if(trans.deal_type == DEAL_TYPE_BUY || trans.deal_type == DEAL_TYPE_SELL)
      {
         // 获取交易历史信息
         if(HistoryDealSelect(trans.deal))
         {
            double profit = HistoryDealGetDouble(trans.deal, DEAL_PROFIT);
            
            // 记录交易结果到历史数组
            g_recentTrades[g_tradeIndex % 5] = (profit > 0) ? 1 : 0;
            g_tradeIndex++;
            
            Print(StringFormat("平仓: 票号=%d, 盈亏=%.2f, 结果=%s", 
                              trans.deal, profit, (profit > 0) ? "盈利" : "亏损"));
            
            // 更新盈冲输缩倍数
            UpdateWinLossMultiplier();
            Print(StringFormat("当前盈冲输缩倍数: %.2f", g_winLossMultiplier));
         }
      }
   }
}