//+------------------------------------------------------------------+
//|                                    DayTradeMultiTimeframe.mq5 |
//|                                  Copyright 2024, TradingSystem |
//|                                    日内多时间周期交易系统       |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, TradingSystem"
#property link      ""
#property version   "1.00"
#property description "日内交易手册EA - 1H方向/15M设伏/5M入场的多时间周期策略"

// 包含所有模块
#include "MultiTimeframeAnalyzer.mqh"
#include "DayTradeSignalModule.mqh"
#include "DayTradeRiskManager.mqh"
#include "DayTradePositionManager.mqh"

//+------------------------------------------------------------------+
//| 输入参数                                                         |
//+------------------------------------------------------------------+
input group "=== 基础设置 ==="
input int InpMagicNumber = 20241103;           // EA魔术号
input string InpTradeComment = "DayTrade_MTF";  // 交易备注

input group "=== 时间周期设置 ==="
input ENUM_TIMEFRAMES InpAnchorTimeframe = PERIOD_H1;    // 锚定时间周期(方向判断)
input ENUM_TIMEFRAMES InpSetupTimeframe = PERIOD_M15;    // 设伏时间周期(回调确认)
input ENUM_TIMEFRAMES InpTriggerTimeframe = PERIOD_M5;   // 触发时间周期(精准入场)

input group "=== EMA均线参数 ==="
input int InpEMA_Period = 20;                  // EMA周期
input ENUM_MA_METHOD InpEMA_Method = MODE_EMA; // 均线计算方法
input ENUM_APPLIED_PRICE InpEMA_Price = PRICE_CLOSE; // 均线应用价格

input group "=== 趋势确认参数 ==="
input double InpTrendStrengthThreshold = 0.7;  // 趋势强度阈值(0-1)
input int InpMinTrendBars = 3;                 // 最小趋势确认K线数
input double InpPullbackMaxPercent = 0.618;    // 最大回调百分比

input group "=== 突破确认参数 ==="
input double InpBreakoutMinPips = 5.0;         // 最小突破点数
input double InpVolumeMultiplier = 1.2;        // 成交量放大倍数
input bool InpRequireStrongCandle = true;      // 要求标志性阳线

input group "=== 风险管理参数 ==="
input double InpRiskPercent = 1.0;             // 单笔风险百分比
input double InpStopLossATR = 2.0;             // 止损ATR倍数
input double InpTakeProfitRatio = 2.0;         // 止盈风险比
input int InpMaxPositionTime = 60;             // 最大持仓时间(分钟)

input group "=== 交易时间控制 ==="
input string InpStartTime = "09:00";           // 开始交易时间
input string InpEndTime = "15:00";             // 结束交易时间
input bool InpCloseAtEndTime = true;           // 收盘时强制平仓

//+------------------------------------------------------------------+
//| 全局变量                                                         |
//+------------------------------------------------------------------+
CMultiTimeframeAnalyzer* g_mtfAnalyzer;        // 多时间周期分析器
CDayTradeSignalModule* g_signalModule;         // 信号模块
CDayTradeRiskManager* g_riskManager;           // 风险管理器
CDayTradePositionManager* g_positionManager;   // 仓位管理器

datetime g_lastBarTime;                        // 上次处理的K线时间
bool g_isInitialized = false;                  // 初始化标志

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("=== 日内多时间周期交易EA启动 ===");
    
    // 验证时间周期设置
    if(!ValidateTimeframes())
    {
        Print("错误: 时间周期设置不正确");
        return INIT_PARAMETERS_INCORRECT;
    }
    
    // 创建分析器和管理器实例
    g_mtfAnalyzer = new CMultiTimeframeAnalyzer();
    g_signalModule = new CDayTradeSignalModule();
    g_riskManager = new CDayTradeRiskManager();
    g_positionManager = new CDayTradePositionManager();
    
    // 初始化各模块
    if(!InitializeModules())
    {
        Print("错误: 模块初始化失败");
        return INIT_FAILED;
    }
    
    g_isInitialized = true;
    g_lastBarTime = 0;
    
    Print("日内多时间周期交易EA初始化成功");
    Print("锚定周期: ", EnumToString(InpAnchorTimeframe));
    Print("设伏周期: ", EnumToString(InpSetupTimeframe));
    Print("触发周期: ", EnumToString(InpTriggerTimeframe));
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("=== 日内多时间周期交易EA关闭 ===");
    
    // 清理资源
    if(g_mtfAnalyzer != NULL)
    {
        delete g_mtfAnalyzer;
        g_mtfAnalyzer = NULL;
    }
    
    if(g_signalModule != NULL)
    {
        delete g_signalModule;
        g_signalModule = NULL;
    }
    
    if(g_riskManager != NULL)
    {
        delete g_riskManager;
        g_riskManager = NULL;
    }
    
    if(g_positionManager != NULL)
    {
        delete g_positionManager;
        g_positionManager = NULL;
    }
    
    Print("资源清理完成，EA已关闭");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    if(!g_isInitialized)
        return;
        
    // 检查是否有新K线
    datetime currentBarTime = iTime(Symbol(), InpTriggerTimeframe, 0);
    if(currentBarTime == g_lastBarTime)
        return;
        
    g_lastBarTime = currentBarTime;
    
    // 检查交易时间
    if(!IsWithinTradingHours())
    {
        // 如果超出交易时间且设置了强制平仓，则平仓所有持仓
        if(InpCloseAtEndTime && g_positionManager.HasOpenPositions())
        {
            g_positionManager.CloseAllPositions("交易时间结束");
        }
        return;
    }
    
    // 执行主要交易逻辑
    ProcessTradingLogic();
}

//+------------------------------------------------------------------+
//| 主要交易逻辑处理                                                 |
//+------------------------------------------------------------------+
void ProcessTradingLogic()
{
    // 1. 更新多时间周期分析
    g_mtfAnalyzer.UpdateAnalysis(Symbol());
    
    // 2. 检查现有持仓管理
    g_positionManager.ManageExistingPositions();
    
    // 3. 如果已有持仓，不开新仓
    if(g_positionManager.HasOpenPositions())
        return;
    
    // 4. 执行日内交易Checklist检查
    ENUM_SIGNAL_TYPE signal = CheckDayTradeConditions();
    
    // 5. 如果信号有效，执行开仓
    if(signal != SIGNAL_NONE)
    {
        ExecuteTradeSignal(signal);
    }
}

//+------------------------------------------------------------------+
//| 日内交易条件检查 (完整Checklist)                                 |
//+------------------------------------------------------------------+
ENUM_SIGNAL_TYPE CheckDayTradeConditions()
{
    Print("=== 开始日内交易Checklist检查 ===");
    
    // A. 日内方向检查 (1小时图)
    if(!CheckAnchorTrend())
    {
        Print("❌ Checklist项目1: 1小时图趋势不明确");
        return SIGNAL_NONE;
    }
    Print("✅ Checklist项目1: 1小时图趋势明确向上");
    
    // B. 战术设伏检查 (15分钟图)
    if(!CheckSetupPullback())
    {
        Print("❌ Checklist项目2: 15分钟图回调条件不满足");
        return SIGNAL_NONE;
    }
    Print("✅ Checklist项目2: 15分钟图处于健康回调");
    
    if(!CheckSetupRecovery())
    {
        Print("❌ Checklist项目3: 15分钟图未出现回调结束信号");
        return SIGNAL_NONE;
    }
    Print("✅ Checklist项目3: 15分钟图出现回调结束信号");
    
    // C. 精准入场检查 (5分钟图)
    if(!CheckTriggerBreakout())
    {
        Print("❌ Checklist项目4: 5分钟图未突破EMA20");
        return SIGNAL_NONE;
    }
    Print("✅ Checklist项目4: 5分钟图突破EMA20");
    
    if(!CheckTriggerConfirmation())
    {
        Print("❌ Checklist项目5: 5分钟图突破未得到确认");
        return SIGNAL_NONE;
    }
    Print("✅ Checklist项目5: 5分钟图突破得到确认");
    
    Print("🎉 所有Checklist条件满足，生成做多信号！");
    return SIGNAL_BUY;
}

//+------------------------------------------------------------------+
//| 验证时间周期设置                                                 |
//+------------------------------------------------------------------+
bool ValidateTimeframes()
{
    // 确保时间周期从大到小排列
    if(InpAnchorTimeframe <= InpSetupTimeframe ||
       InpSetupTimeframe <= InpTriggerTimeframe)
    {
        Print("错误: 时间周期必须按从大到小排列 (锚定 > 设伏 > 触发)");
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| 初始化所有模块                                                   |
//+------------------------------------------------------------------+
bool InitializeModules()
{
    // 初始化多时间周期分析器
    if(!g_mtfAnalyzer.Initialize(InpAnchorTimeframe, InpSetupTimeframe, InpTriggerTimeframe))
    {
        Print("多时间周期分析器初始化失败");
        return false;
    }

    // 初始化信号模块
    if(!g_signalModule.Initialize(InpEMA_Period, InpBreakoutMinPips, InpVolumeMultiplier))
    {
        Print("信号模块初始化失败");
        return false;
    }

    // 初始化风险管理器
    if(!g_riskManager.Initialize(InpRiskPercent, InpStopLossATR, InpTakeProfitRatio))
    {
        Print("风险管理器初始化失败");
        return false;
    }

    // 初始化仓位管理器
    if(!g_positionManager.Initialize(InpMagicNumber, InpMaxPositionTime))
    {
        Print("仓位管理器初始化失败");
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| 检查是否在交易时间内                                             |
//+------------------------------------------------------------------+
bool IsWithinTradingHours()
{
    MqlDateTime currentTime;
    TimeToStruct(TimeCurrent(), currentTime);

    // 解析开始和结束时间
    string startParts[];
    string endParts[];
    StringSplit(InpStartTime, ':', startParts);
    StringSplit(InpEndTime, ':', endParts);

    if(ArraySize(startParts) != 2 || ArraySize(endParts) != 2)
        return true; // 如果时间格式错误，默认允许交易

    int startHour = (int)StringToInteger(startParts[0]);
    int startMinute = (int)StringToInteger(startParts[1]);
    int endHour = (int)StringToInteger(endParts[0]);
    int endMinute = (int)StringToInteger(endParts[1]);

    int currentMinutes = currentTime.hour * 60 + currentTime.min;
    int startMinutes = startHour * 60 + startMinute;
    int endMinutes = endHour * 60 + endMinute;

    return (currentMinutes >= startMinutes && currentMinutes <= endMinutes);
}

//+------------------------------------------------------------------+
//| A. 检查锚定时间周期趋势 (1小时图方向判断)                        |
//+------------------------------------------------------------------+
bool CheckAnchorTrend()
{
    return g_mtfAnalyzer.IsAnchorTrendBullish();
}

//+------------------------------------------------------------------+
//| B1. 检查设伏时间周期回调 (15分钟图回调确认)                      |
//+------------------------------------------------------------------+
bool CheckSetupPullback()
{
    return g_mtfAnalyzer.IsSetupInHealthyPullback();
}

//+------------------------------------------------------------------+
//| B2. 检查设伏时间周期恢复 (15分钟图回调结束信号)                  |
//+------------------------------------------------------------------+
bool CheckSetupRecovery()
{
    return g_mtfAnalyzer.IsSetupShowingRecovery();
}

//+------------------------------------------------------------------+
//| C1. 检查触发时间周期突破 (5分钟图EMA20突破)                      |
//+------------------------------------------------------------------+
bool CheckTriggerBreakout()
{
    return g_signalModule.IsTriggerBreakoutConfirmed(Symbol(), InpTriggerTimeframe);
}

//+------------------------------------------------------------------+
//| C2. 检查触发时间周期确认 (5分钟图突破确认)                       |
//+------------------------------------------------------------------+
bool CheckTriggerConfirmation()
{
    return g_signalModule.IsTriggerConfirmationValid(Symbol(), InpTriggerTimeframe);
}

//+------------------------------------------------------------------+
//| 执行交易信号                                                     |
//+------------------------------------------------------------------+
void ExecuteTradeSignal(ENUM_SIGNAL_TYPE signal)
{
    if(signal == SIGNAL_BUY)
    {
        // 计算仓位大小和风险参数
        double lotSize = g_riskManager.CalculatePositionSize(Symbol());
        double stopLoss = g_riskManager.CalculateStopLoss(Symbol(), signal);
        double takeProfit = g_riskManager.CalculateTakeProfit(Symbol(), signal, stopLoss);

        // 执行开仓
        if(g_positionManager.OpenPosition(Symbol(), ORDER_TYPE_BUY, lotSize, stopLoss, takeProfit))
        {
            Print("✅ 成功开多仓 - 手数:", lotSize, " 止损:", stopLoss, " 止盈:", takeProfit);
        }
        else
        {
            Print("❌ 开多仓失败");
        }
    }
}
