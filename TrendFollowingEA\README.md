# 趋势跟踪交易系统 (Trend Following EA)

## 系统概述

这是一个基于趋势跟踪哲学的全自动交易系统，采用模块化架构设计。系统的核心理念是"截断亏损，让利润奔跑"，通过识别市场趋势状态，在趋势确立时进场，并通过动态止损管理来保护利润。

## 核心哲学

**趋势跟踪与突破**
- 不预测市场顶部或底部，只跟随已经形成的市场力量
- 利润来源：抓住大幅度的单边行情（"奔跑的利润"）
- 生存之道：在没有趋势或趋势反转时快速离场（"截断亏损"）

## 系统架构

### 四大核心模块

#### 1. 市场状态识别模块 (MarketStateAnalyzer)
**职责：** 判断当前市场是否适合运行趋势策略

**技术指标：**
- **ATR波动率分析：** ATR(14) > ATR(60) * 1.5 时认为波动率扩张
- **ADX趋势强度：** ADX(14) > 25 时认为存在值得交易的趋势
- **Guppy多重均线组：** 
  - 短期组：3, 5, 8, 10, 12, 15周期EMA
  - 长期组：30, 35, 40, 45, 50, 60周期EMA
  - 趋势确认：短期组完全在长期组之上且两组均线发散

**输出状态：**
- `MARKET_STATE_TRENDING_UP` - 上升趋势
- `MARKET_STATE_TRENDING_DOWN` - 下降趋势  
- `MARKET_STATE_RANGING` - 震荡市

#### 2. 入场信号模块 (EntrySignalModule)
**职责：** 在趋势确认后寻找精确的入场时机

**入场方法：**
- **唐奇安通道突破：** 价格突破过去N周期（默认20）的最高/最低价
- **波动性突破：** 价格突破 EMA(20) ± 2*ATR(20) 的通道

**信号类型：**
- `SIGNAL_BUY` - 买入信号
- `SIGNAL_SELL` - 卖出信号
- `SIGNAL_NONE` - 无信号

#### 3. 出场管理模块 (ExitManager)
**职责：** 管理止损和利润保护

**止损策略：**
- **初始止损：** 开仓价 ± K*ATR(14)，K默认为2.5
- **吊灯止损（Chandelier Exit）：** 
  - 多头：过去N周期最高价 - K*ATR(14)
  - 空头：过去N周期最低价 + K*ATR(14)
- **跟踪止损：** 止损只朝有利方向移动，永不后退

#### 4. 资金与头寸管理模块 (PositionManager)
**职责：** 实现"赢冲输缩"的资金管理

**风险控制：**
- **固定风险百分比：** 每笔交易风险固定为账户资金的1%
- **动态头寸计算：** 根据ATR止损距离自动调整仓位大小
- **回撤保护：** 账户回撤15%时，风险降至0.5%

**加仓策略（金字塔）：**
- **加仓条件：** 价格朝有利方向移动1.5*ATR时触发
- **加仓规模：** 每层递减20%，最多3层
- **风险控制：** 加仓后将所有仓位止损移至保本位置

## 文件结构

```
趋势跟踪系统/
├── TrendFollowingEA.mq5          # 主EA文件
├── MarketStateAnalyzer.mqh       # 市场状态识别模块
├── EntrySignalModule.mqh         # 入场信号模块
├── ExitManager.mqh               # 出场管理模块
├── PositionManager.mqh           # 仓位管理模块
├── SystemConfig.mqh              # 系统配置文件
└── README.md                     # 说明文档
```

## 参数配置

### 市场状态识别参数
- `InpATRPeriod` (14) - ATR周期
- `InpATRLongPeriod` (60) - ATR长期周期
- `InpATRMultiplier` (1.5) - ATR倍数阈值
- `InpADXPeriod` (14) - ADX周期
- `InpADXThreshold` (25.0) - ADX趋势阈值

### 入场信号参数
- `InpDonchianPeriod` (20) - 唐奇安通道周期
- `InpBreakoutEMAPeriod` (20) - 突破均线周期
- `InpBreakoutATRMultiplier` (2.0) - 突破ATR倍数

### 出场管理参数
- `InpInitialStopATRMultiplier` (2.5) - 初始止损ATR倍数
- `InpTrailingStopATRMultiplier` (2.0) - 跟踪止损ATR倍数
- `InpChandelierPeriod` (22) - 吊灯止损周期

### 资金管理参数
- `InpRiskPercentage` (1.0) - 单笔风险百分比
- `InpMaxDrawdownPercent` (15.0) - 最大回撤百分比
- `InpReducedRiskPercent` (0.5) - 回撤后降低风险百分比
- `InpPyramidATRMultiplier` (1.5) - 加仓ATR倍数
- `InpMaxPyramidLevels` (3) - 最大加仓层数

## 使用说明

### 1. 安装步骤
1. 将所有文件复制到MT5的`MQL5/Experts/`目录下
2. 在MT5中编译`TrendFollowingEA.mq5`
3. 将EA拖拽到图表上
4. 根据需要调整参数设置

### 2. 推荐设置
- **时间周期：** H1或H4图表效果最佳
- **货币对：** 主要货币对（EUR/USD, GBP/USD, USD/JPY等）
- **最小资金：** 建议至少$10,000以上账户
- **点差要求：** 平均点差不超过3点

### 3. 风险提示
- 趋势跟踪系统在震荡市中可能产生较多小额亏损
- 系统依赖大幅单边行情获利，需要耐心等待
- 建议在模拟账户充分测试后再使用实盘

## 系统特点

### 优势
1. **模块化设计：** 各模块职责清晰，易于维护和扩展
2. **自适应性强：** 基于ATR的动态参数调整
3. **风险控制严格：** 多层次风险管理机制
4. **回撤保护：** 动态调整风险暴露
5. **无需预测：** 纯粹跟随市场趋势

### 适用场景
- 中长期趋势交易
- 波动率较高的市场环境
- 有明显趋势特征的品种
- 资金规模较大的账户

### 不适用场景
- 高频交易
- 震荡市为主的环境
- 点差过大的品种
- 资金规模过小的账户

## 性能监控

系统会定期输出状态报告，包括：
- 账户余额和净值
- 当前回撤水平
- 风险模式状态
- 当前仓位层数

## 技术支持

如有问题或建议，请联系开发团队。

---

**免责声明：** 本系统仅供学习和研究使用，实盘交易存在风险，请谨慎使用。