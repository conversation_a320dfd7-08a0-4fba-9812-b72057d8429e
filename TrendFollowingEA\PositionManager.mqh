//+------------------------------------------------------------------+
//|                                            PositionManager.mqh |
//|                                  Copyright 2024, TradingSystem |
//|                                           资金与头寸管理模块    |
//+------------------------------------------------------------------+

#ifndef POSITION_MANAGER_H
#define POSITION_MANAGER_H

#include <Trade\Trade.mqh>
#include "MarketStateAnalyzer.mqh"
#include "EntrySignalModule.mqh"

//--- 加仓信息结构
struct SPyramidLevel
{
    ulong ticket;           // 订单号
    double entryPrice;      // 入场价格
    double volume;          // 仓位大小
    datetime entryTime;     // 入场时间
    double profitTarget;    // 加仓触发价格
};

//+------------------------------------------------------------------+
//| 仓位管理器类                                                     |
//+------------------------------------------------------------------+
class CPositionManager
{
private:
    CTrade m_trade;
    
    // 风险管理参数
    double m_riskPercentage;        // 单笔风险百分比
    double m_maxDrawdownPercent;    // 最大回撤百分比
    double m_reducedRiskPercent;    // 回撤后降低风险百分比
    double m_pyramidATRMultiplier;  // 加仓ATR倍数
    int m_maxPyramidLevels;         // 最大加仓层数
    int m_maxPositionsPerSymbol;    // 单一品种最大持仓笔数
    
    // 交易参数
    int m_magicNumber;
    string m_tradeComment;
    
    // 账户状态跟踪
    double m_accountBalance;
    double m_accountEquity;
    double m_peakEquity;            // 历史最高净值
    double m_currentDrawdown;       // 当前回撤
    bool m_isInDrawdown;            // 是否处于回撤状态
    
    // 加仓管理
    SPyramidLevel m_pyramidLevels[];
    ENUM_POSITION_TYPE m_currentDirection;
    bool m_hasPrimaryPosition;
    
    // 指标句柄
    int m_atrHandle;
    
    // 私有方法
    void UpdateAccountStatus();
    double CalculatePositionSize(double riskAmount, double stopDistance);
    double GetCurrentRiskPercentage();
    bool CanOpenNewPosition();
    bool CanAddPyramid();
    double CalculatePyramidTriggerPrice(double lastEntryPrice, ENUM_POSITION_TYPE posType);
    void CleanupClosedPositions();
    
public:
    CPositionManager();
    ~CPositionManager();
    
    bool Init(double riskPercentage, double maxDrawdownPercent, double reducedRiskPercent,
              double pyramidATRMultiplier, int maxPyramidLevels, int maxPositionsPerSymbol,
              int magicNumber, string tradeComment);
    
    void Update();
    void ProcessEntry(ENUM_SIGNAL_TYPE signal, ENUM_MARKET_STATE marketState);
    void ProcessPyramiding();
    void OnTradeEvent();
    void CheckSystemHealth();
    
    // 获取状态信息
    double GetCurrentDrawdown() const { return m_currentDrawdown; }
    bool IsInDrawdown() const { return m_isInDrawdown; }
    int GetPyramidLevels() const { return ArraySize(m_pyramidLevels); }
    double GetTotalVolume();
    double GetCurrentATR();
};

//+------------------------------------------------------------------+
//| 构造函数                                                         |
//+------------------------------------------------------------------+
CPositionManager::CPositionManager()
{
    m_atrHandle = INVALID_HANDLE;
    m_accountBalance = 0;
    m_accountEquity = 0;
    m_peakEquity = 0;
    m_currentDrawdown = 0;
    m_isInDrawdown = false;
    m_hasPrimaryPosition = false;
    m_currentDirection = POSITION_TYPE_BUY;
    
    ArrayResize(m_pyramidLevels, 0);
}

//+------------------------------------------------------------------+
//| 析构函数                                                         |
//+------------------------------------------------------------------+
CPositionManager::~CPositionManager()
{
    if(m_atrHandle != INVALID_HANDLE)
        IndicatorRelease(m_atrHandle);
}

//+------------------------------------------------------------------+
//| 初始化函数                                                       |
//+------------------------------------------------------------------+
bool CPositionManager::Init(double riskPercentage, double maxDrawdownPercent, 
                            double reducedRiskPercent, double pyramidATRMultiplier,
                            int maxPyramidLevels, int maxPositionsPerSymbol,
                            int magicNumber, string tradeComment)
{
    m_riskPercentage = riskPercentage;
    m_maxDrawdownPercent = maxDrawdownPercent;
    m_reducedRiskPercent = reducedRiskPercent;
    m_pyramidATRMultiplier = pyramidATRMultiplier;
    m_maxPyramidLevels = maxPyramidLevels;
    m_maxPositionsPerSymbol = maxPositionsPerSymbol;
    m_magicNumber = magicNumber;
    m_tradeComment = tradeComment;
    
    // 设置交易魔术数字
    m_trade.SetExpertMagicNumber(m_magicNumber);
    
    // 创建ATR指标
    m_atrHandle = iATR(_Symbol, PERIOD_CURRENT, 14);
    if(m_atrHandle == INVALID_HANDLE)
    {
        Print("仓位管理器：ATR指标创建失败");
        return false;
    }
    
    // 初始化账户状态
    UpdateAccountStatus();
    m_peakEquity = m_accountEquity;
    
    Print("仓位管理器初始化成功");
    return true;
}

//+------------------------------------------------------------------+
//| 更新函数                                                         |
//+------------------------------------------------------------------+
void CPositionManager::Update()
{
    UpdateAccountStatus();
    CleanupClosedPositions();
}

//+------------------------------------------------------------------+
//| 处理入场信号                                                     |
//+------------------------------------------------------------------+
void CPositionManager::ProcessEntry(ENUM_SIGNAL_TYPE signal, ENUM_MARKET_STATE marketState)
{
    if(signal == SIGNAL_NONE)
        return;
    
    // 检查是否可以开新仓
    if(!CanOpenNewPosition())
        return;
    
    // 确定交易方向
    ENUM_ORDER_TYPE orderType;
    ENUM_POSITION_TYPE positionType;
    
    if(signal == SIGNAL_BUY)
    {
        orderType = ORDER_TYPE_BUY;
        positionType = POSITION_TYPE_BUY;
    }
    else
    {
        orderType = ORDER_TYPE_SELL;
        positionType = POSITION_TYPE_SELL;
    }
    
    // 如果已有相反方向的仓位，先平仓
    if(m_hasPrimaryPosition && m_currentDirection != positionType)
    {
        // 平掉所有现有仓位
        for(int i = PositionsTotal() - 1; i >= 0; i--)
        {
            ulong ticket = PositionGetTicket(i);
            if(PositionSelectByTicket(ticket) && 
               PositionGetString(POSITION_SYMBOL) == _Symbol &&
               PositionGetInteger(POSITION_MAGIC) == m_magicNumber)
            {
                m_trade.PositionClose(ticket);
            }
        }
        
        // 清空加仓记录
        ArrayResize(m_pyramidLevels, 0);
        m_hasPrimaryPosition = false;
    }
    
    // 计算仓位大小
    double currentPrice = (signal == SIGNAL_BUY) ? 
                         SymbolInfoDouble(_Symbol, SYMBOL_ASK) : 
                         SymbolInfoDouble(_Symbol, SYMBOL_BID);
    
    double atr = GetCurrentATR();
    double stopDistance = 2.5 * atr; // 初始止损距离
    double stopPrice = (signal == SIGNAL_BUY) ? 
                      currentPrice - stopDistance : 
                      currentPrice + stopDistance;
    
    double riskAmount = m_accountBalance * GetCurrentRiskPercentage() / 100.0;
    double volume = CalculatePositionSize(riskAmount, stopDistance);
    
    if(volume <= 0)
    {
        Print("计算出的仓位大小无效：", volume);
        return;
    }
    
    // 执行开仓
    bool result = false;
    if(signal == SIGNAL_BUY)
        result = m_trade.Buy(volume, _Symbol, 0, stopPrice, 0, m_tradeComment);
    else
        result = m_trade.Sell(volume, _Symbol, 0, stopPrice, 0, m_tradeComment);
    
    if(result)
    {
        ulong ticket = m_trade.ResultOrder();
        Print("开仓成功 - 订单号：", ticket, " 仓位：", volume, " 止损：", stopPrice);
        
        // 记录主仓位
        m_hasPrimaryPosition = true;
        m_currentDirection = positionType;
        
        // 添加到加仓管理（确保不超过最大层级）
        if(ArraySize(m_pyramidLevels) < m_maxPyramidLevels)
        {
            SPyramidLevel newLevel;
            newLevel.ticket = ticket;
            newLevel.entryPrice = currentPrice;
            newLevel.volume = volume;
            newLevel.entryTime = TimeCurrent();
            newLevel.profitTarget = CalculatePyramidTriggerPrice(currentPrice, positionType);
            
            int newSize = ArraySize(m_pyramidLevels) + 1;
            ArrayResize(m_pyramidLevels, newSize);
            m_pyramidLevels[newSize - 1] = newLevel;
            
            Print("已记录第", newSize, "层仓位，最大允许层级：", m_maxPyramidLevels);
        }
        else
        {
            Print("警告：已达到最大加仓层级，无法记录更多仓位");
        }
    }
    else
    {
        Print("开仓失败 - 错误代码：", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| 处理加仓逻辑                                                     |
//+------------------------------------------------------------------+
void CPositionManager::ProcessPyramiding()
{
    if(!m_hasPrimaryPosition || !CanAddPyramid())
        return;
    
    double currentPrice = (m_currentDirection == POSITION_TYPE_BUY) ? 
                         SymbolInfoDouble(_Symbol, SYMBOL_BID) : 
                         SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    
    // 检查是否达到加仓条件
    int levelsCount = ArraySize(m_pyramidLevels);
    if(levelsCount == 0)
        return;
    
    SPyramidLevel lastLevel = m_pyramidLevels[levelsCount - 1];
    
    bool shouldAddPyramid = false;
    if(m_currentDirection == POSITION_TYPE_BUY && currentPrice >= lastLevel.profitTarget)
        shouldAddPyramid = true;
    else if(m_currentDirection == POSITION_TYPE_SELL && currentPrice <= lastLevel.profitTarget)
        shouldAddPyramid = true;
    
    if(!shouldAddPyramid)
        return;
    
    // 计算加仓大小（递减）
    double baseVolume = m_pyramidLevels[0].volume;
    double pyramidVolume = baseVolume * (1.0 - 0.2 * levelsCount); // 每层递减20%
    
    if(pyramidVolume < SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN))
        return;
    
    // 计算新的止损
    double atr = GetCurrentATR();
    double stopDistance = 2.0 * atr;
    double stopPrice = (m_currentDirection == POSITION_TYPE_BUY) ? 
                      currentPrice - stopDistance : 
                      currentPrice + stopDistance;
    
    // 执行加仓
    bool result = false;
    if(m_currentDirection == POSITION_TYPE_BUY)
        result = m_trade.Buy(pyramidVolume, _Symbol, 0, stopPrice, 0, m_tradeComment + "_Pyramid");
    else
        result = m_trade.Sell(pyramidVolume, _Symbol, 0, stopPrice, 0, m_tradeComment + "_Pyramid");
    
    if(result)
    {
        ulong ticket = m_trade.ResultOrder();
        Print("加仓成功 - 订单号：", ticket, " 仓位：", pyramidVolume, " 层级：", levelsCount + 1);
        
        // 记录新的加仓层级（再次检查层级限制）
        if(ArraySize(m_pyramidLevels) < m_maxPyramidLevels)
        {
            SPyramidLevel newLevel;
            newLevel.ticket = ticket;
            newLevel.entryPrice = currentPrice;
            newLevel.volume = pyramidVolume;
            newLevel.entryTime = TimeCurrent();
            newLevel.profitTarget = CalculatePyramidTriggerPrice(currentPrice, m_currentDirection);
            
            int newSize = ArraySize(m_pyramidLevels) + 1;
            ArrayResize(m_pyramidLevels, newSize);
            m_pyramidLevels[newSize - 1] = newLevel;
            
            Print("已记录第", newSize, "层加仓，最大允许层级：", m_maxPyramidLevels);
        }
        else
        {
            Print("警告：加仓成功但已达到最大层级限制，无法记录更多层级");
        }
        
        // 更新所有仓位的止损到保本或小幅盈利位置
        double breakEvenPrice = m_pyramidLevels[0].entryPrice;
        for(int i = 0; i < PositionsTotal(); i++)
        {
            ulong posTicket = PositionGetTicket(i);
            if(PositionSelectByTicket(posTicket) && 
               PositionGetString(POSITION_SYMBOL) == _Symbol &&
               PositionGetInteger(POSITION_MAGIC) == m_magicNumber)
            {
                double newStop = breakEvenPrice;
                if(m_currentDirection == POSITION_TYPE_BUY)
                    newStop = MathMax(newStop, breakEvenPrice + atr * 0.5);
                else
                    newStop = MathMin(newStop, breakEvenPrice - atr * 0.5);
                
                m_trade.PositionModify(posTicket, newStop, 0);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 更新账户状态                                                     |
//+------------------------------------------------------------------+
void CPositionManager::UpdateAccountStatus()
{
    m_accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    m_accountEquity = AccountInfoDouble(ACCOUNT_EQUITY);
    
    // 更新历史最高净值
    if(m_accountEquity > m_peakEquity)
        m_peakEquity = m_accountEquity;
    
    // 计算当前回撤
    m_currentDrawdown = (m_peakEquity - m_accountEquity) / m_peakEquity * 100.0;
    
    // 判断是否处于回撤状态
    m_isInDrawdown = (m_currentDrawdown >= m_maxDrawdownPercent);
}

//+------------------------------------------------------------------+
//| 计算仓位大小                                                     |
//+------------------------------------------------------------------+
double CPositionManager::CalculatePositionSize(double riskAmount, double stopDistance)
{
    double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    double minVolume = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double maxVolume = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double volumeStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    
    if(tickValue == 0 || tickSize == 0 || stopDistance <= 0)
        return 0;
    
    // 计算每手的风险金额
    double riskPerLot = stopDistance / tickSize * tickValue;
    
    // 计算手数
    double volume = riskAmount / riskPerLot;
    
    // 调整到合法的手数
    volume = MathMax(volume, minVolume);
    volume = MathMin(volume, maxVolume);
    volume = MathFloor(volume / volumeStep) * volumeStep;
    
    return volume;
}

//+------------------------------------------------------------------+
//| 获取当前风险百分比                                               |
//+------------------------------------------------------------------+
double CPositionManager::GetCurrentRiskPercentage()
{
    if(m_isInDrawdown)
        return m_reducedRiskPercent;
    else
        return m_riskPercentage;
}

//+------------------------------------------------------------------+
//| 检查是否可以开新仓                                               |
//+------------------------------------------------------------------+
bool CPositionManager::CanOpenNewPosition()
{
    // 检查账户状态
    if(m_accountBalance <= 0)
        return false;
    
    // 检查单一品种持仓笔数限制
    int currentPositions = 0;
    for(int i = 0; i < PositionsTotal(); i++)
    {
        ulong ticket = PositionGetTicket(i);
        if(PositionSelectByTicket(ticket) && 
           PositionGetString(POSITION_SYMBOL) == _Symbol &&
           PositionGetInteger(POSITION_MAGIC) == m_magicNumber)
        {
            currentPositions++;
        }
    }
    
    if(currentPositions >= m_maxPositionsPerSymbol)
    {
        Print("已达到单一品种最大持仓笔数限制：", m_maxPositionsPerSymbol);
        return false;
    }
    
    // 检查是否有足够的保证金
    double freeMargin = AccountInfoDouble(ACCOUNT_MARGIN_FREE);
    double marginRequired = SymbolInfoDouble(_Symbol, SYMBOL_MARGIN_INITIAL);
    
    if(freeMargin < marginRequired * 2) // 保留2倍保证金作为缓冲
        return false;
    
    return true;
}

//+------------------------------------------------------------------+
//| 检查是否可以加仓                                                 |
//+------------------------------------------------------------------+
bool CPositionManager::CanAddPyramid()
{
    // 检查加仓层级限制
    if(ArraySize(m_pyramidLevels) >= m_maxPyramidLevels)
    {
        Print("已达到最大加仓层级：", m_maxPyramidLevels);
        return false;
    }
    
    // 检查是否处于回撤状态
    if(m_isInDrawdown)
    {
        Print("处于回撤状态，禁止加仓");
        return false;
    }
    
    // 检查单一品种持仓笔数限制（加仓也算一笔新仓位）
    int currentPositions = 0;
    for(int i = 0; i < PositionsTotal(); i++)
    {
        ulong ticket = PositionGetTicket(i);
        if(PositionSelectByTicket(ticket) && 
           PositionGetString(POSITION_SYMBOL) == _Symbol &&
           PositionGetInteger(POSITION_MAGIC) == m_magicNumber)
        {
            currentPositions++;
        }
    }
    
    if(currentPositions >= m_maxPositionsPerSymbol)
    {
        Print("加仓受限：已达到单一品种最大持仓笔数限制：", m_maxPositionsPerSymbol);
        return false;
    }
    
    // 检查保证金充足性
    double freeMargin = AccountInfoDouble(ACCOUNT_MARGIN_FREE);
    double marginRequired = SymbolInfoDouble(_Symbol, SYMBOL_MARGIN_INITIAL);
    
    if(freeMargin < marginRequired * 2)
    {
        Print("加仓受限：保证金不足");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 计算加仓触发价格                                                 |
//+------------------------------------------------------------------+
double CPositionManager::CalculatePyramidTriggerPrice(double lastEntryPrice, ENUM_POSITION_TYPE posType)
{
    double atr = GetCurrentATR();
    double triggerDistance = m_pyramidATRMultiplier * atr;
    
    if(posType == POSITION_TYPE_BUY)
        return lastEntryPrice + triggerDistance;
    else
        return lastEntryPrice - triggerDistance;
}

//+------------------------------------------------------------------+
//| 清理已关闭的仓位                                                 |
//+------------------------------------------------------------------+
void CPositionManager::CleanupClosedPositions()
{
    for(int i = ArraySize(m_pyramidLevels) - 1; i >= 0; i--)
    {
        if(!PositionSelectByTicket(m_pyramidLevels[i].ticket))
        {
            // 仓位已关闭，从数组中移除
            for(int j = i; j < ArraySize(m_pyramidLevels) - 1; j++)
            {
                m_pyramidLevels[j] = m_pyramidLevels[j + 1];
            }
            ArrayResize(m_pyramidLevels, ArraySize(m_pyramidLevels) - 1);
        }
    }
    
    // 检查是否还有主仓位
    if(ArraySize(m_pyramidLevels) == 0)
        m_hasPrimaryPosition = false;
}

//+------------------------------------------------------------------+
//| 交易事件处理                                                     |
//+------------------------------------------------------------------+
void CPositionManager::OnTradeEvent()
{
    CleanupClosedPositions();
}

//+------------------------------------------------------------------+
//| 系统健康检查                                                     |
//+------------------------------------------------------------------+
void CPositionManager::CheckSystemHealth()
{
    UpdateAccountStatus();
    
    // 统计当前持仓情况
    int currentPositions = 0;
    double totalVolume = 0;
    for(int i = 0; i < PositionsTotal(); i++)
    {
        ulong ticket = PositionGetTicket(i);
        if(PositionSelectByTicket(ticket) && 
           PositionGetString(POSITION_SYMBOL) == _Symbol &&
           PositionGetInteger(POSITION_MAGIC) == m_magicNumber)
        {
            currentPositions++;
            totalVolume += PositionGetDouble(POSITION_VOLUME);
        }
    }
    
    // 输出系统状态
    static datetime lastReport = 0;
    if(TimeCurrent() - lastReport > 3600) // 每小时报告一次
    {
        Print("=== 系统状态报告 ===");
        Print("账户余额：", DoubleToString(m_accountBalance, 2));
        Print("账户净值：", DoubleToString(m_accountEquity, 2));
        Print("历史最高：", DoubleToString(m_peakEquity, 2));
        Print("当前回撤：", DoubleToString(m_currentDrawdown, 2), "%");
        Print("风险状态：", m_isInDrawdown ? "降低风险模式" : "正常模式");
        Print("当前持仓笔数：", currentPositions, "/", m_maxPositionsPerSymbol);
        Print("当前仓位层数：", ArraySize(m_pyramidLevels), "/", m_maxPyramidLevels);
        Print("总持仓量：", DoubleToString(totalVolume, 2));
        Print("当前风险比例：", DoubleToString(GetCurrentRiskPercentage(), 2), "%");
        Print("==================");
        
        lastReport = TimeCurrent();
    }
    
    // 检查异常情况
    if(currentPositions > m_maxPositionsPerSymbol)
    {
        Print("警告：当前持仓笔数(", currentPositions, ")超过限制(", m_maxPositionsPerSymbol, ")");
    }
    
    if(ArraySize(m_pyramidLevels) > m_maxPyramidLevels)
    {
        Print("警告：当前加仓层数(", ArraySize(m_pyramidLevels), ")超过限制(", m_maxPyramidLevels, ")");
    }
}

//+------------------------------------------------------------------+
//| 获取总仓位                                                       |
//+------------------------------------------------------------------+
double CPositionManager::GetTotalVolume()
{
    double totalVolume = 0;
    for(int i = 0; i < ArraySize(m_pyramidLevels); i++)
    {
        totalVolume += m_pyramidLevels[i].volume;
    }
    return totalVolume;
}

//+------------------------------------------------------------------+
//| 获取当前ATR                                                      |
//+------------------------------------------------------------------+
double CPositionManager::GetCurrentATR()
{
    double atrValues[];
    if(CopyBuffer(m_atrHandle, 0, 1, 1, atrValues) <= 0)
        return 0.0;
    return atrValues[0];
}

#endif // POSITION_MANAGER_H