//+------------------------------------------------------------------+
//|                                     OneClickTrader_UI.mqh       |
//|                                    Copyright 2025, Your Company |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Company"
#property link      "https://www.mql5.com"

#include "OneClickTrader_Calculator.mqh"

//--- UI常量
#define PANEL_WIDTH 280
#define PANEL_HEIGHT 450
#define BUTTON_WIDTH 80
#define BUTTON_HEIGHT 25
#define MARGIN 10
#define LINE_HEIGHT 20

//+------------------------------------------------------------------+
//| UI管理类                                                          |
//+------------------------------------------------------------------+
class COneClickTraderUI
{
private:
    ENUM_BASE_CORNER m_corner;
    color m_bg_color;
    color m_active_color;
    color m_inactive_color;
    color m_text_color;
    
    int m_panel_x;
    int m_panel_y;
    
public:
    COneClickTraderUI();
    ~COneClickTraderUI();
    
    bool Initialize(ENUM_BASE_CORNER corner, color bg_color, color active_color, 
                   color inactive_color, color text_color);
    void CreatePanel();
    void DestroyPanel();
    void UpdateDisplay(const STradeCalculation &calc);
    void UpdateRiskButtons(ENUM_RISK_MODE active_mode);
    
private:
    void CreateBackground();
    void CreateRiskButtons();
    void CreateTradeButtons();
    void CreateInfoLabels();
    void SetObjectPosition(string name, int x, int y, int width = 0, int height = 0);
    void CreateButton(string name, string text, int x, int y, int width, int height, color bg_color);
    void CreateLabel(string name, string text, int x, int y, color text_color);
};

//+------------------------------------------------------------------+
//| 构造函数                                                          |
//+------------------------------------------------------------------+
COneClickTraderUI::COneClickTraderUI()
{
    m_corner = CORNER_LEFT_UPPER;
    m_bg_color = clrDarkSlateGray;
    m_active_color = clrLimeGreen;
    m_inactive_color = clrGray;
    m_text_color = clrWhite;
    m_panel_x = 20;
    m_panel_y = 50;
}

//+------------------------------------------------------------------+
//| 析构函数                                                          |
//+------------------------------------------------------------------+
COneClickTraderUI::~COneClickTraderUI()
{
}

//+------------------------------------------------------------------+
//| 初始化                                                            |
//+------------------------------------------------------------------+
bool COneClickTraderUI::Initialize(ENUM_BASE_CORNER corner, color bg_color, color active_color, 
                                 color inactive_color, color text_color)
{
    m_corner = corner;
    m_bg_color = bg_color;
    m_active_color = active_color;
    m_inactive_color = inactive_color;
    m_text_color = text_color;
    
    return true;
}

//+------------------------------------------------------------------+
//| 创建面板                                                          |
//+------------------------------------------------------------------+
void COneClickTraderUI::CreatePanel()
{
    CreateBackground();
    CreateRiskButtons();
    CreateTradeButtons();
    CreateInfoLabels();
    
    ChartRedraw();
}

//+------------------------------------------------------------------+
//| 销毁面板                                                          |
//+------------------------------------------------------------------+
void COneClickTraderUI::DestroyPanel()
{
    // 删除所有UI对象
    ObjectDelete(0, "OCT_Background");
    ObjectDelete(0, "OCT_Title");
    ObjectDelete(0, "OCT_TrialButton");
    ObjectDelete(0, "OCT_StandardButton");
    ObjectDelete(0, "OCT_HeavyButton");
    ObjectDelete(0, "OCT_BuyButton");
    ObjectDelete(0, "OCT_SellButton");
    ObjectDelete(0, "OCT_HighBuyButton");
    ObjectDelete(0, "OCT_HighSellButton");
    ObjectDelete(0, "OCT_LowBuyButton");
    ObjectDelete(0, "OCT_LowSellButton");
    ObjectDelete(0, "OCT_ModeLabel");
    ObjectDelete(0, "OCT_RiskLabel");
    ObjectDelete(0, "OCT_LossLabel");
    ObjectDelete(0, "OCT_ProfitLabel");
    ObjectDelete(0, "OCT_LotLabel");
    ObjectDelete(0, "OCT_SLLabel");
    ObjectDelete(0, "OCT_TPLabel");
    ObjectDelete(0, "OCT_ATRLabel");
    
    ChartRedraw();
}

//+------------------------------------------------------------------+
//| 更新显示信息                                                      |
//+------------------------------------------------------------------+
void COneClickTraderUI::UpdateDisplay(const STradeCalculation &calc)
{
    string mode_name = "";
    switch(calc.risk_mode)
    {
        case RISK_TRIAL: mode_name = "试仓"; break;
        case RISK_STANDARD: mode_name = "标准仓"; break;
        case RISK_HEAVY: mode_name = "重仓"; break;
    }
    
    ObjectSetString(0, "OCT_ModeLabel", OBJPROP_TEXT, "当前模式: " + mode_name);
    ObjectSetString(0, "OCT_RiskLabel", OBJPROP_TEXT, StringFormat("风险比例: %.1f%%", calc.risk_percent));
    ObjectSetString(0, "OCT_LossLabel", OBJPROP_TEXT, StringFormat("预估亏损: %.2f", calc.estimated_loss));
    ObjectSetString(0, "OCT_ProfitLabel", OBJPROP_TEXT, StringFormat("预估盈利: %.2f", calc.estimated_profit));
    ObjectSetString(0, "OCT_LotLabel", OBJPROP_TEXT, StringFormat("计算手数: %.2f", calc.lot_size));
    ObjectSetString(0, "OCT_SLLabel", OBJPROP_TEXT, StringFormat("止损价格: %.5f", calc.sl_price));
    ObjectSetString(0, "OCT_TPLabel", OBJPROP_TEXT, StringFormat("止盈价格: %.5f", calc.tp_price));
    ObjectSetString(0, "OCT_ATRLabel", OBJPROP_TEXT, StringFormat("ATR值: %.5f", calc.atr_value));
}

//+------------------------------------------------------------------+
//| 更新风险按钮状态                                                  |
//+------------------------------------------------------------------+
void COneClickTraderUI::UpdateRiskButtons(ENUM_RISK_MODE active_mode)
{
    // 重置所有按钮颜色
    ObjectSetInteger(0, "OCT_TrialButton", OBJPROP_BGCOLOR, m_inactive_color);
    ObjectSetInteger(0, "OCT_StandardButton", OBJPROP_BGCOLOR, m_inactive_color);
    ObjectSetInteger(0, "OCT_HeavyButton", OBJPROP_BGCOLOR, m_inactive_color);
    
    // 设置激活按钮颜色
    switch(active_mode)
    {
        case RISK_TRIAL:
            ObjectSetInteger(0, "OCT_TrialButton", OBJPROP_BGCOLOR, m_active_color);
            break;
        case RISK_STANDARD:
            ObjectSetInteger(0, "OCT_StandardButton", OBJPROP_BGCOLOR, m_active_color);
            break;
        case RISK_HEAVY:
            ObjectSetInteger(0, "OCT_HeavyButton", OBJPROP_BGCOLOR, m_active_color);
            break;
    }
}

//+------------------------------------------------------------------+
//| 创建背景                                                          |
//+------------------------------------------------------------------+
void COneClickTraderUI::CreateBackground()
{
    ObjectCreate(0, "OCT_Background", OBJ_RECTANGLE_LABEL, 0, 0, 0);
    SetObjectPosition("OCT_Background", m_panel_x, m_panel_y, PANEL_WIDTH, PANEL_HEIGHT);
    ObjectSetInteger(0, "OCT_Background", OBJPROP_BGCOLOR, m_bg_color);
    ObjectSetInteger(0, "OCT_Background", OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, "OCT_Background", OBJPROP_CORNER, m_corner);
    ObjectSetInteger(0, "OCT_Background", OBJPROP_STYLE, STYLE_SOLID);
    ObjectSetInteger(0, "OCT_Background", OBJPROP_WIDTH, 1);
    ObjectSetInteger(0, "OCT_Background", OBJPROP_BACK, false);
    ObjectSetInteger(0, "OCT_Background", OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, "OCT_Background", OBJPROP_SELECTED, false);
    ObjectSetInteger(0, "OCT_Background", OBJPROP_HIDDEN, true);
    
    // 标题
    CreateLabel("OCT_Title", "一键交易面板", m_panel_x + MARGIN, m_panel_y + MARGIN, clrYellow);
    ObjectSetInteger(0, "OCT_Title", OBJPROP_FONTSIZE, 12);
}

//+------------------------------------------------------------------+
//| 创建风险按钮                                                      |
//+------------------------------------------------------------------+
void COneClickTraderUI::CreateRiskButtons()
{
    int y_pos = m_panel_y + 40;
    
    CreateButton("OCT_TrialButton", "试仓", m_panel_x + MARGIN, y_pos, BUTTON_WIDTH, BUTTON_HEIGHT, m_inactive_color);
    CreateButton("OCT_StandardButton", "标准仓", m_panel_x + MARGIN + BUTTON_WIDTH + 5, y_pos, BUTTON_WIDTH, BUTTON_HEIGHT, m_active_color);
    CreateButton("OCT_HeavyButton", "重仓", m_panel_x + MARGIN + BUTTON_WIDTH * 2 + 10, y_pos, BUTTON_WIDTH, BUTTON_HEIGHT, m_inactive_color);
}

//+------------------------------------------------------------------+
//| 创建交易按钮                                                      |
//+------------------------------------------------------------------+
void COneClickTraderUI::CreateTradeButtons()
{
    int y_pos = m_panel_y + PANEL_HEIGHT - 140;
    
    // 市价交易按钮
    CreateButton("OCT_BuyButton", "市价BUY", m_panel_x + MARGIN, y_pos, 120, 25, clrGreen);
    CreateButton("OCT_SellButton", "市价SELL", m_panel_x + MARGIN + 130, y_pos, 120, 25, clrRed);
    
    // 高点上方挂单按钮
    y_pos += 30;
    CreateButton("OCT_HighBuyButton", "高点上方挂多", m_panel_x + MARGIN, y_pos, 120, 25, clrLimeGreen);
    CreateButton("OCT_HighSellButton", "高点上方挂空", m_panel_x + MARGIN + 130, y_pos, 120, 25, clrOrange);
    
    // 低点下方挂单按钮
    y_pos += 30;
    CreateButton("OCT_LowBuyButton", "低点下方挂多", m_panel_x + MARGIN, y_pos, 120, 25, clrDodgerBlue);
    CreateButton("OCT_LowSellButton", "低点下方挂空", m_panel_x + MARGIN + 130, y_pos, 120, 25, clrOrangeRed);
}

//+------------------------------------------------------------------+
//| 创建信息标签                                                      |
//+------------------------------------------------------------------+
void COneClickTraderUI::CreateInfoLabels()
{
    int start_y = m_panel_y + 80;
    int line_spacing = 25;
    
    CreateLabel("OCT_ModeLabel", "当前模式: 标准仓", m_panel_x + MARGIN, start_y, m_text_color);
    CreateLabel("OCT_RiskLabel", "风险比例: 0.5%", m_panel_x + MARGIN, start_y + line_spacing, m_text_color);
    CreateLabel("OCT_LossLabel", "预估亏损: 0.00", m_panel_x + MARGIN, start_y + line_spacing * 2, m_text_color);
    CreateLabel("OCT_ProfitLabel", "预估盈利: 0.00", m_panel_x + MARGIN, start_y + line_spacing * 3, m_text_color);
    CreateLabel("OCT_LotLabel", "计算手数: 0.00", m_panel_x + MARGIN, start_y + line_spacing * 4, m_text_color);
    CreateLabel("OCT_SLLabel", "止损价格: 0.00000", m_panel_x + MARGIN, start_y + line_spacing * 5, m_text_color);
    CreateLabel("OCT_TPLabel", "止盈价格: 0.00000", m_panel_x + MARGIN, start_y + line_spacing * 6, m_text_color);
    CreateLabel("OCT_ATRLabel", "ATR值: 0.00000", m_panel_x + MARGIN, start_y + line_spacing * 7, m_text_color);
}

//+------------------------------------------------------------------+
//| 设置对象位置                                                      |
//+------------------------------------------------------------------+
void COneClickTraderUI::SetObjectPosition(string name, int x, int y, int width = 0, int height = 0)
{
    ObjectSetInteger(0, name, OBJPROP_CORNER, m_corner);
    ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
    ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
    
    if(width > 0)
        ObjectSetInteger(0, name, OBJPROP_XSIZE, width);
    if(height > 0)
        ObjectSetInteger(0, name, OBJPROP_YSIZE, height);
}

//+------------------------------------------------------------------+
//| 创建按钮                                                          |
//+------------------------------------------------------------------+
void COneClickTraderUI::CreateButton(string name, string text, int x, int y, int width, int height, color bg_color)
{
    ObjectCreate(0, name, OBJ_BUTTON, 0, 0, 0);
    SetObjectPosition(name, x, y, width, height);
    ObjectSetString(0, name, OBJPROP_TEXT, text);
    ObjectSetInteger(0, name, OBJPROP_BGCOLOR, bg_color);
    ObjectSetInteger(0, name, OBJPROP_COLOR, clrWhite);
    ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 10);
    ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
}

//+------------------------------------------------------------------+
//| 创建标签                                                          |
//+------------------------------------------------------------------+
void COneClickTraderUI::CreateLabel(string name, string text, int x, int y, color text_color)
{
    ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
    SetObjectPosition(name, x, y);
    ObjectSetString(0, name, OBJPROP_TEXT, text);
    ObjectSetInteger(0, name, OBJPROP_COLOR, text_color);
    ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 9);
    ObjectSetString(0, name, OBJPROP_FONT, "Arial");
    ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
}