//+------------------------------------------------------------------+
//|                                               ExitManager.mqh |
//|                                  Copyright 2024, TradingSystem |
//|                                             出场管理模块        |
//+------------------------------------------------------------------+

#ifndef EXIT_MANAGER_H
#define EXIT_MANAGER_H

#include <Trade\Trade.mqh>

//--- 出场类型枚举
enum ENUM_EXIT_TYPE
{
    EXIT_NONE,              // 无出场
    EXIT_INITIAL_STOP,      // 初始止损
    EXIT_TRAILING_STOP,     // 跟踪止损
    EXIT_CHANDELIER_STOP    // 吊灯止损
};

//--- 仓位信息结构
struct SPositionInfo
{
    ulong ticket;           // 订单号
    double openPrice;       // 开仓价格
    double volume;          // 仓位大小
    ENUM_POSITION_TYPE type;// 仓位类型
    double initialStop;     // 初始止损
    double currentStop;     // 当前止损
    datetime openTime;      // 开仓时间
    bool isTrailing;        // 是否启用跟踪
};

//+------------------------------------------------------------------+
//| 出场管理器类                                                     |
//+------------------------------------------------------------------+
class CExitManager
{
private:
    CTrade m_trade;
    
    // 止损参数
    double m_initialStopATRMultiplier;
    double m_trailingStopATRMultiplier;
    int m_chandelierPeriod;
    int m_atrPeriod;
    
    // 指标句柄
    int m_atrHandle;
    
    // 仓位跟踪
    SPositionInfo m_positions[];
    
    // 私有方法
    void UpdatePositionsList();
    double CalculateInitialStop(ENUM_POSITION_TYPE posType, double openPrice);
    double CalculateChandelierStop(ENUM_POSITION_TYPE posType);
    double CalculateATRTrailingStop(ENUM_POSITION_TYPE posType, double currentStop);
    bool UpdateStopLoss(ulong ticket, double newStop);
    void ProcessSinglePosition(SPositionInfo &pos);
    
public:
    CExitManager();
    ~CExitManager();
    
    bool Init(double initialStopATRMultiplier, double trailingStopATRMultiplier,
              int chandelierPeriod, int atrPeriod);
    
    void Update();
    void ProcessExits();
    
    // 新仓位管理
    bool AddPosition(ulong ticket);
    void RemovePosition(ulong ticket);
    
    // 获取信息
    double GetCurrentATR();
    int GetPositionsCount() const { return ArraySize(m_positions); }
    double GetPositionStop(ulong ticket);
};

//+------------------------------------------------------------------+
//| 构造函数                                                         |
//+------------------------------------------------------------------+
CExitManager::CExitManager()
{
    m_atrHandle = INVALID_HANDLE;
    ArrayResize(m_positions, 0);
}

//+------------------------------------------------------------------+
//| 析构函数                                                         |
//+------------------------------------------------------------------+
CExitManager::~CExitManager()
{
    if(m_atrHandle != INVALID_HANDLE)
        IndicatorRelease(m_atrHandle);
}

//+------------------------------------------------------------------+
//| 初始化函数                                                       |
//+------------------------------------------------------------------+
bool CExitManager::Init(double initialStopATRMultiplier, double trailingStopATRMultiplier,
                        int chandelierPeriod, int atrPeriod)
{
    m_initialStopATRMultiplier = initialStopATRMultiplier;
    m_trailingStopATRMultiplier = trailingStopATRMultiplier;
    m_chandelierPeriod = chandelierPeriod;
    m_atrPeriod = atrPeriod;
    
    // 创建ATR指标
    m_atrHandle = iATR(_Symbol, PERIOD_CURRENT, m_atrPeriod);
    if(m_atrHandle == INVALID_HANDLE)
    {
        Print("出场管理器：ATR指标创建失败");
        return false;
    }
    
    Print("出场管理器初始化成功");
    return true;
}

//+------------------------------------------------------------------+
//| 更新函数                                                         |
//+------------------------------------------------------------------+
void CExitManager::Update()
{
    UpdatePositionsList();
}

//+------------------------------------------------------------------+
//| 处理出场逻辑                                                     |
//+------------------------------------------------------------------+
void CExitManager::ProcessExits()
{
    for(int i = ArraySize(m_positions) - 1; i >= 0; i--)
    {
        ProcessSinglePosition(m_positions[i]);
    }
}

//+------------------------------------------------------------------+
//| 更新仓位列表                                                     |
//+------------------------------------------------------------------+
void CExitManager::UpdatePositionsList()
{
    // 清空当前列表
    ArrayResize(m_positions, 0);
    
    // 重新构建仓位列表
    for(int i = 0; i < PositionsTotal(); i++)
    {
        ulong ticket = PositionGetTicket(i);
        if(ticket > 0 && PositionSelectByTicket(ticket))
        {
            if(PositionGetString(POSITION_SYMBOL) == _Symbol)
            {
                AddPosition(ticket);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 添加仓位                                                         |
//+------------------------------------------------------------------+
bool CExitManager::AddPosition(ulong ticket)
{
    if(!PositionSelectByTicket(ticket))
        return false;
    
    // 检查是否已存在
    for(int i = 0; i < ArraySize(m_positions); i++)
    {
        if(m_positions[i].ticket == ticket)
            return true; // 已存在
    }
    
    // 添加新仓位
    int newSize = ArraySize(m_positions) + 1;
    ArrayResize(m_positions, newSize);
    
    SPositionInfo newPos;
    newPos.ticket = ticket;
    newPos.openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
    newPos.volume = PositionGetDouble(POSITION_VOLUME);
    newPos.type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
    newPos.openTime = (datetime)PositionGetInteger(POSITION_TIME);
    newPos.isTrailing = false;
    
    // 计算初始止损
    newPos.initialStop = CalculateInitialStop(newPos.type, newPos.openPrice);
    newPos.currentStop = newPos.initialStop;
    
    m_positions[newSize - 1] = newPos;
    
    // 设置初始止损
    UpdateStopLoss(ticket, newPos.initialStop);
    
    Print("添加仓位跟踪：", ticket, " 初始止损：", newPos.initialStop);
    return true;
}

//+------------------------------------------------------------------+
//| 移除仓位                                                         |
//+------------------------------------------------------------------+
void CExitManager::RemovePosition(ulong ticket)
{
    for(int i = 0; i < ArraySize(m_positions); i++)
    {
        if(m_positions[i].ticket == ticket)
        {
            // 移除元素
            for(int j = i; j < ArraySize(m_positions) - 1; j++)
            {
                m_positions[j] = m_positions[j + 1];
            }
            ArrayResize(m_positions, ArraySize(m_positions) - 1);
            break;
        }
    }
}

//+------------------------------------------------------------------+
//| 计算初始止损                                                     |
//+------------------------------------------------------------------+
double CExitManager::CalculateInitialStop(ENUM_POSITION_TYPE posType, double openPrice)
{
    double atr = GetCurrentATR();
    if(atr <= 0)
        return 0;
    
    double stopDistance = m_initialStopATRMultiplier * atr;
    
    if(posType == POSITION_TYPE_BUY)
        return openPrice - stopDistance;
    else
        return openPrice + stopDistance;
}

//+------------------------------------------------------------------+
//| 计算吊灯止损                                                     |
//+------------------------------------------------------------------+
double CExitManager::CalculateChandelierStop(ENUM_POSITION_TYPE posType)
{
    double atr = GetCurrentATR();
    if(atr <= 0)
        return 0;
    
    double high[], low[];
    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    
    if(CopyHigh(_Symbol, PERIOD_CURRENT, 0, m_chandelierPeriod + 1, high) <= 0 ||
       CopyLow(_Symbol, PERIOD_CURRENT, 0, m_chandelierPeriod + 1, low) <= 0)
    {
        return 0;
    }
    
    if(posType == POSITION_TYPE_BUY)
    {
        // 多头：过去N周期最高价 - K * ATR
        double maxHigh = high[ArrayMaximum(high, 1, m_chandelierPeriod)];
        return maxHigh - m_trailingStopATRMultiplier * atr;
    }
    else
    {
        // 空头：过去N周期最低价 + K * ATR
        double minLow = low[ArrayMinimum(low, 1, m_chandelierPeriod)];
        return minLow + m_trailingStopATRMultiplier * atr;
    }
}

//+------------------------------------------------------------------+
//| 计算ATR跟踪止损                                                  |
//+------------------------------------------------------------------+
double CExitManager::CalculateATRTrailingStop(ENUM_POSITION_TYPE posType, double currentStop)
{
    double chandelierStop = CalculateChandelierStop(posType);
    
    if(chandelierStop <= 0)
        return currentStop;
    
    if(posType == POSITION_TYPE_BUY)
    {
        // 多头止损只能上移
        return MathMax(currentStop, chandelierStop);
    }
    else
    {
        // 空头止损只能下移
        return MathMin(currentStop, chandelierStop);
    }
}

//+------------------------------------------------------------------+
//| 更新止损                                                         |
//+------------------------------------------------------------------+
bool CExitManager::UpdateStopLoss(ulong ticket, double newStop)
{
    if(!PositionSelectByTicket(ticket))
        return false;
    
    double currentSL = PositionGetDouble(POSITION_SL);
    
    // 检查是否需要更新
    ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
    
    bool needUpdate = false;
    if(posType == POSITION_TYPE_BUY && newStop > currentSL)
        needUpdate = true;
    else if(posType == POSITION_TYPE_SELL && newStop < currentSL)
        needUpdate = true;
    
    if(!needUpdate)
        return true;
    
    // 执行修改
    double tp = PositionGetDouble(POSITION_TP);
    return m_trade.PositionModify(ticket, newStop, tp);
}

//+------------------------------------------------------------------+
//| 处理单个仓位                                                     |
//+------------------------------------------------------------------+
void CExitManager::ProcessSinglePosition(SPositionInfo &pos)
{
    if(!PositionSelectByTicket(pos.ticket))
    {
        RemovePosition(pos.ticket);
        return;
    }
    
    // 检查仓位是否有浮盈（启用跟踪止损的条件）
    double currentPrice = (pos.type == POSITION_TYPE_BUY) ? 
                         SymbolInfoDouble(_Symbol, SYMBOL_BID) : 
                         SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    
    bool hasProfit = false;
    if(pos.type == POSITION_TYPE_BUY && currentPrice > pos.openPrice)
        hasProfit = true;
    else if(pos.type == POSITION_TYPE_SELL && currentPrice < pos.openPrice)
        hasProfit = true;
    
    // 如果有浮盈，启用跟踪止损
    if(hasProfit && !pos.isTrailing)
    {
        pos.isTrailing = true;
        Print("仓位 ", pos.ticket, " 启用跟踪止损");
    }
    
    // 如果启用了跟踪，更新止损
    if(pos.isTrailing)
    {
        double newStop = CalculateATRTrailingStop(pos.type, pos.currentStop);
        
        if(newStop != pos.currentStop)
        {
            if(UpdateStopLoss(pos.ticket, newStop))
            {
                pos.currentStop = newStop;
                Print("仓位 ", pos.ticket, " 止损更新至：", newStop);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 获取当前ATR值                                                    |
//+------------------------------------------------------------------+
double CExitManager::GetCurrentATR()
{
    double atrValues[];
    if(CopyBuffer(m_atrHandle, 0, 1, 1, atrValues) <= 0)
        return 0.0;
    return atrValues[0];
}

//+------------------------------------------------------------------+
//| 获取仓位止损                                                     |
//+------------------------------------------------------------------+
double CExitManager::GetPositionStop(ulong ticket)
{
    for(int i = 0; i < ArraySize(m_positions); i++)
    {
        if(m_positions[i].ticket == ticket)
            return m_positions[i].currentStop;
    }
    return 0.0;
}

#endif // EXIT_MANAGER_H