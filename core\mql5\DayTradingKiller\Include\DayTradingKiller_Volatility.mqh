//+------------------------------------------------------------------+
//|                            DayTradingKiller_Volatility.mqh      |
//|                                    日内交易大杀器 - 波动率分析类  |
//|                                    Copyright 2025, Your Company |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Company"
#property link      "https://www.mql5.com"

//--- 交易时段枚举
enum ENUM_TRADING_SESSION
{
    SESSION_ASIA,    // 亚洲时段
    SESSION_LONDON,  // 伦敦时段
    SESSION_OVERLAP, // 重叠时段
    SESSION_NEWYORK, // 纽约时段
    SESSION_QUIET    // 静默时段
};

//--- 波动率分析结构
struct SVolatilityAnalysis
{
    ENUM_TRADING_SESSION current_session;
    string session_name;
    double session_volatility;
    double daily_range;
    double remaining_range;
    double completion_percent;
    string time_info;
    double expected_range;
    bool is_high_volatility_time;
    color session_color;
};

//+------------------------------------------------------------------+
//| 波动率分析类                                                      |
//+------------------------------------------------------------------+
class CDayTradingVolatility
{
private:
    int m_atr_handle;
    int m_atr_period;
    
    // 时段定义 (GMT+0)
    int m_asia_start_hour;
    int m_asia_end_hour;
    int m_london_start_hour;
    int m_london_end_hour;
    int m_newyork_start_hour;
    int m_newyork_end_hour;
    
    // 波动率数据
    double m_daily_high;
    double m_daily_low;
    double m_session_high;
    double m_session_low;
    datetime m_last_day;
    
    SVolatilityAnalysis m_current_analysis;
    
public:
    CDayTradingVolatility();
    ~CDayTradingVolatility();
    
    bool Initialize(int atr_period);
    void UpdateVolatilityAnalysis();
    void GetCurrentAnalysis(SVolatilityAnalysis &analysis);
    bool IsHighVolatilityTime();
    double GetExpectedDailyRange();
    
private:
    ENUM_TRADING_SESSION GetCurrentSession();
    string GetSessionName(ENUM_TRADING_SESSION session);
    color GetSessionColor(ENUM_TRADING_SESSION session);
    void UpdateDailyRange();
    void UpdateSessionRange();
    double CalculateSessionVolatility();
    double GetATRValue();
    bool IsNewDay();
    string FormatTimeInfo();
    double GetHistoricalSessionVolatility(ENUM_TRADING_SESSION session);
};

//+------------------------------------------------------------------+
//| 构造函数                                                          |
//+------------------------------------------------------------------+
CDayTradingVolatility::CDayTradingVolatility()
{
    m_atr_handle = INVALID_HANDLE;
    m_atr_period = 14;
    
    // 设置时段时间 (GMT+0)
    m_asia_start_hour = 0;      // 00:00 GMT
    m_asia_end_hour = 9;        // 09:00 GMT
    m_london_start_hour = 8;    // 08:00 GMT
    m_london_end_hour = 17;     // 17:00 GMT
    m_newyork_start_hour = 13;  // 13:00 GMT
    m_newyork_end_hour = 22;    // 22:00 GMT
    
    m_daily_high = 0;
    m_daily_low = 0;
    m_session_high = 0;
    m_session_low = 0;
    m_last_day = 0;
}

//+------------------------------------------------------------------+
//| 析构函数                                                          |
//+------------------------------------------------------------------+
CDayTradingVolatility::~CDayTradingVolatility()
{
    if(m_atr_handle != INVALID_HANDLE)
        IndicatorRelease(m_atr_handle);
}

//+------------------------------------------------------------------+
//| 初始化                                                            |
//+------------------------------------------------------------------+
bool CDayTradingVolatility::Initialize(int atr_period)
{
    m_atr_period = atr_period;
    
    // 创建ATR指标
    m_atr_handle = iATR(Symbol(), PERIOD_CURRENT, m_atr_period);
    if(m_atr_handle == INVALID_HANDLE)
    {
        Print("创建ATR指标失败");
        return false;
    }
    
    // 初始化日内数据
    UpdateDailyRange();
    UpdateSessionRange();
    
    Print("波动率分析初始化成功");
    return true;
}

//+------------------------------------------------------------------+
//| 更新波动率分析                                                    |
//+------------------------------------------------------------------+
void CDayTradingVolatility::UpdateVolatilityAnalysis()
{
    // 检查是否新的一天
    if(IsNewDay())
    {
        UpdateDailyRange();
    }
    
    UpdateSessionRange();
    
    // 获取当前时段
    ENUM_TRADING_SESSION current_session = GetCurrentSession();
    
    // 计算当前分析数据
    m_current_analysis.current_session = current_session;
    m_current_analysis.session_name = GetSessionName(current_session);
    m_current_analysis.session_color = GetSessionColor(current_session);
    m_current_analysis.session_volatility = CalculateSessionVolatility();
    m_current_analysis.daily_range = m_daily_high - m_daily_low;
    m_current_analysis.expected_range = GetExpectedDailyRange();
    
    // 计算完成度
    if(m_current_analysis.expected_range > 0)
    {
        m_current_analysis.completion_percent = (m_current_analysis.daily_range / m_current_analysis.expected_range) * 100.0;
        m_current_analysis.remaining_range = m_current_analysis.expected_range - m_current_analysis.daily_range;
    }
    else
    {
        m_current_analysis.completion_percent = 0;
        m_current_analysis.remaining_range = 0;
    }
    
    // 判断是否高波动时段
    m_current_analysis.is_high_volatility_time = IsHighVolatilityTime();
    
    // 格式化时间信息
    m_current_analysis.time_info = FormatTimeInfo();
}

//+------------------------------------------------------------------+
//| 获取当前分析结果                                                  |
//+------------------------------------------------------------------+
void CDayTradingVolatility::GetCurrentAnalysis(SVolatilityAnalysis &analysis)
{
    analysis = m_current_analysis;
}

//+------------------------------------------------------------------+
//| 判断是否高波动时段                                                |
//+------------------------------------------------------------------+
bool CDayTradingVolatility::IsHighVolatilityTime()
{
    ENUM_TRADING_SESSION session = GetCurrentSession();
    
    // 伦敦开盘、纽约开盘、重叠时段为高波动
    return (session == SESSION_LONDON || session == SESSION_NEWYORK || session == SESSION_OVERLAP);
}

//+------------------------------------------------------------------+
//| 获取预期日内波幅                                                  |
//+------------------------------------------------------------------+
double CDayTradingVolatility::GetExpectedDailyRange()
{
    double atr_value = GetATRValue();
    if(atr_value <= 0)
        return 0;
    
    // 预期日内波幅约为ATR的1.2-1.5倍
    return atr_value * 1.3;
}

//+------------------------------------------------------------------+
//| 获取当前交易时段                                                  |
//+------------------------------------------------------------------+
ENUM_TRADING_SESSION CDayTradingVolatility::GetCurrentSession()
{
    MqlDateTime dt;
    TimeToStruct(TimeGMT(), dt);
    int current_hour = dt.hour;
    
    // 判断重叠时段 (伦敦和纽约重叠)
    if(current_hour >= m_newyork_start_hour && current_hour < m_london_end_hour)
        return SESSION_OVERLAP;
    
    // 判断各个时段
    if(current_hour >= m_asia_start_hour && current_hour < m_asia_end_hour)
        return SESSION_ASIA;
    else if(current_hour >= m_london_start_hour && current_hour < m_london_end_hour)
        return SESSION_LONDON;
    else if(current_hour >= m_newyork_start_hour && current_hour < m_newyork_end_hour)
        return SESSION_NEWYORK;
    else
        return SESSION_QUIET;
}

//+------------------------------------------------------------------+
//| 获取时段名称                                                      |
//+------------------------------------------------------------------+
string CDayTradingVolatility::GetSessionName(ENUM_TRADING_SESSION session)
{
    switch(session)
    {
        case SESSION_ASIA:
            return "亚洲时段";
        case SESSION_LONDON:
            return "伦敦时段";
        case SESSION_OVERLAP:
            return "欧美重叠";
        case SESSION_NEWYORK:
            return "纽约时段";
        case SESSION_QUIET:
            return "静默时段";
        default:
            return "未知时段";
    }
}

//+------------------------------------------------------------------+
//| 获取时段颜色                                                      |
//+------------------------------------------------------------------+
color CDayTradingVolatility::GetSessionColor(ENUM_TRADING_SESSION session)
{
    switch(session)
    {
        case SESSION_ASIA:
            return clrLightBlue;
        case SESSION_LONDON:
            return clrGold;
        case SESSION_OVERLAP:
            return clrLimeGreen;
        case SESSION_NEWYORK:
            return clrOrange;
        case SESSION_QUIET:
            return clrGray;
        default:
            return clrWhite;
    }
}

//+------------------------------------------------------------------+
//| 更新日内波幅                                                      |
//+------------------------------------------------------------------+
void CDayTradingVolatility::UpdateDailyRange()
{
    datetime today = TimeCurrent();
    MqlDateTime dt;
    TimeToStruct(today, dt);
    dt.hour = 0;
    dt.min = 0;
    dt.sec = 0;
    datetime day_start = StructToTime(dt);
    
    // 获取今日最高最低价
    double high_prices[], low_prices[];
    int bars_today = Bars(Symbol(), PERIOD_M1, day_start, today);
    
    if(bars_today > 0)
    {
        if(CopyHigh(Symbol(), PERIOD_M1, day_start, bars_today, high_prices) > 0 &&
           CopyLow(Symbol(), PERIOD_M1, day_start, bars_today, low_prices) > 0)
        {
            m_daily_high = high_prices[ArrayMaximum(high_prices)];
            m_daily_low = low_prices[ArrayMinimum(low_prices)];
        }
    }
    
    m_last_day = day_start;
}

//+------------------------------------------------------------------+
//| 更新时段波幅                                                      |
//+------------------------------------------------------------------+
void CDayTradingVolatility::UpdateSessionRange()
{
    // 获取当前时段开始时间
    MqlDateTime dt;
    TimeToStruct(TimeGMT(), dt);
    
    ENUM_TRADING_SESSION session = GetCurrentSession();
    int session_start_hour = 0;
    
    switch(session)
    {
        case SESSION_ASIA:
            session_start_hour = m_asia_start_hour;
            break;
        case SESSION_LONDON:
            session_start_hour = m_london_start_hour;
            break;
        case SESSION_OVERLAP:
            session_start_hour = m_newyork_start_hour;
            break;
        case SESSION_NEWYORK:
            session_start_hour = m_newyork_start_hour;
            break;
        default:
            session_start_hour = dt.hour;
            break;
    }
    
    dt.hour = session_start_hour;
    dt.min = 0;
    dt.sec = 0;
    datetime session_start = StructToTime(dt);
    
    // 获取时段内的最高最低价
    double high_prices[], low_prices[];
    int bars_session = Bars(Symbol(), PERIOD_M1, session_start, TimeGMT());
    
    if(bars_session > 0)
    {
        if(CopyHigh(Symbol(), PERIOD_M1, session_start, bars_session, high_prices) > 0 &&
           CopyLow(Symbol(), PERIOD_M1, session_start, bars_session, low_prices) > 0)
        {
            m_session_high = high_prices[ArrayMaximum(high_prices)];
            m_session_low = low_prices[ArrayMinimum(low_prices)];
        }
    }
}

//+------------------------------------------------------------------+
//| 计算时段波动率                                                    |
//+------------------------------------------------------------------+
double CDayTradingVolatility::CalculateSessionVolatility()
{
    if(m_session_high <= 0 || m_session_low <= 0)
        return 0;
    
    return m_session_high - m_session_low;
}

//+------------------------------------------------------------------+
//| 获取ATR值                                                         |
//+------------------------------------------------------------------+
double CDayTradingVolatility::GetATRValue()
{
    if(m_atr_handle == INVALID_HANDLE)
        return 0;
    
    double atr_buffer[1];
    if(CopyBuffer(m_atr_handle, 0, 0, 1, atr_buffer) <= 0)
        return 0;
    
    return atr_buffer[0];
}

//+------------------------------------------------------------------+
//| 检查是否新的一天                                                  |
//+------------------------------------------------------------------+
bool CDayTradingVolatility::IsNewDay()
{
    datetime current_time = TimeCurrent();
    MqlDateTime dt;
    TimeToStruct(current_time, dt);
    dt.hour = 0;
    dt.min = 0;
    dt.sec = 0;
    datetime today_start = StructToTime(dt);
    
    return today_start != m_last_day;
}

//+------------------------------------------------------------------+
//| 格式化时间信息                                                    |
//+------------------------------------------------------------------+
string CDayTradingVolatility::FormatTimeInfo()
{
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    
    return StringFormat("%02d:%02d 本地时间", dt.hour, dt.min);
}

//+------------------------------------------------------------------+
//| 获取历史时段波动率                                                |
//+------------------------------------------------------------------+
double CDayTradingVolatility::GetHistoricalSessionVolatility(ENUM_TRADING_SESSION session)
{
    // 简化实现：返回ATR值作为参考
    return GetATRValue();
}