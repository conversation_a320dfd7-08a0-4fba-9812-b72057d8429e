//+------------------------------------------------------------------+
//|                                                  RiskManager.mqh |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"

//+------------------------------------------------------------------+
//| 风险管理器类                                                        |
//+------------------------------------------------------------------+
//+------------------------------------------------------------------+
//| 交易方向枚举                                                        |
//+------------------------------------------------------------------+
enum ENUM_TRADE_DIRECTION
{
   TRADE_DIRECTION_BOTH = 0,    // 多空都做
   TRADE_DIRECTION_LONG_ONLY,   // 只做多
   TRADE_DIRECTION_SHORT_ONLY   // 只做空
};

//+------------------------------------------------------------------+
//| 开仓方向枚举                                                        |
//+------------------------------------------------------------------+
enum ENUM_OPEN_DIRECTION
{
   OPEN_DIRECTION_AUTO = 0,     // 自动判断方向
   OPEN_DIRECTION_BUY_ONLY,     // 只开多单
   OPEN_DIRECTION_SELL_ONLY     // 只开空单
};

class CRiskManager
{
public:
   double            m_riskPerTrade;        // 每笔风险百分比
   double            m_maxTotalRisk;        // 最大总风险百分比
   double            m_atrMultiplier;       // ATR倍数（止损）
   double            m_riskRewardRatio;     // 风险收益比（止盈:止损）
   int               m_maxPositions;        // 最大持仓数
   ENUM_TRADE_DIRECTION m_tradeDirection;  // 交易方向
   ENUM_OPEN_DIRECTION m_openDirection;    // 开仓方向
   
private:
   double            m_risk_percent;        // 每笔风险百分比
   double            m_max_risk_percent;    // 最大总风险百分比
   double            m_min_lot;             // 最小手数
   double            m_max_lot;             // 最大手数
   int               m_magic_number;        // 魔术数字
   int               m_atr_period;          // ATR周期
   
   // 盈冲输缩参数
   double            m_win_multiplier;      // 盈利倍数
   double            m_loss_multiplier;     // 亏损倍数
   int               m_history_trades;      // 历史交易数量
   double            m_current_multiplier;  // 当前倍数
   
public:
   // 构造函数
                     CRiskManager(double risk_percent = 2.0, double max_risk = 6.0, int max_pos = 3);
                    ~CRiskManager();
   
   // 设置参数
   void              SetRiskPercent(double percent) { m_risk_percent = percent; }
   void              SetMaxRiskPercent(double percent) { m_max_risk_percent = percent; }
   void              SetMaxPositions(int positions) { m_maxPositions = positions; }
   void              SetLotLimits(double min_lot, double max_lot);
   void              SetMagicNumber(int magic) { m_magic_number = magic; }
   void              SetPositionSizing(double win_mult, double loss_mult, int history);
   void              SetRiskRewardRatio(double ratio) { m_riskRewardRatio = ratio; }
   void              SetATRMultiplier(double multiplier) { m_atrMultiplier = multiplier; }
   void              SetATRPeriod(int period) { m_atr_period = period; }
   void              SetTradeDirection(ENUM_TRADE_DIRECTION direction) { m_tradeDirection = direction; }
   void              SetOpenDirection(ENUM_OPEN_DIRECTION direction) { m_openDirection = direction; }
   
   // 风险计算
   double            CalculateLotSize(double entry_price, double stop_loss, string symbol = "");
   double            CalculateLotSizeByATR(double entry_price, ENUM_ORDER_TYPE order_type, string symbol = "");
   double            CalculateStopLoss(double entry_price, ENUM_ORDER_TYPE order_type, string symbol = "");
   double            CalculateTakeProfit(double entry_price, double stop_loss, string symbol = "");
   double            GetATR(int period, string symbol = "");
   bool              IsRiskAcceptable(string symbol = "");
   bool              CanOpenPosition(ENUM_ORDER_TYPE order_type, string symbol = "");
   double            GetCurrentRiskPercent(string symbol = "");
   
   // 盈冲输缩
   void              UpdatePositionMultiplier(string symbol = "");
   double            GetCurrentMultiplier() const { return m_current_multiplier; }
   
   // 持仓管理
   int               GetPositionCount(string symbol = "");
   double            GetTotalProfit(string symbol = "");
   double            GetTotalRisk(string symbol = "");
   
   // 工具函数
   double            NormalizeLot(double lot, string symbol = "");
   double            GetAccountBalance();
   double            GetAccountEquity();
};

//+------------------------------------------------------------------+
//| 构造函数                                                           |
//+------------------------------------------------------------------+
CRiskManager::CRiskManager(double risk_percent, double max_risk, int max_pos)
{
   m_riskPerTrade = risk_percent;
   m_maxTotalRisk = max_risk;
   m_maxPositions = max_pos;
   m_risk_percent = risk_percent;
   m_max_risk_percent = max_risk;
   m_min_lot = 0.01;
   m_max_lot = 10.0;
   m_magic_number = 0;
   m_atr_period = 14;
   m_tradeDirection = TRADE_DIRECTION_BOTH;
   m_openDirection = OPEN_DIRECTION_AUTO;
   
   m_win_multiplier = 1.2;
   m_loss_multiplier = 0.8;
   m_history_trades = 5;
   m_current_multiplier = 1.0;
   m_atrMultiplier = 2.0;
   m_riskRewardRatio = 2.0;
}

//+------------------------------------------------------------------+
//| 析构函数                                                           |
//+------------------------------------------------------------------+
CRiskManager::~CRiskManager()
{
}

//+------------------------------------------------------------------+
//| 设置手数限制                                                        |
//+------------------------------------------------------------------+
void CRiskManager::SetLotLimits(double min_lot, double max_lot)
{
   m_min_lot = min_lot;
   m_max_lot = max_lot;
}

//+------------------------------------------------------------------+
//| 设置盈冲输缩参数                                                     |
//+------------------------------------------------------------------+
void CRiskManager::SetPositionSizing(double win_mult, double loss_mult, int history)
{
   m_win_multiplier = win_mult;
   m_loss_multiplier = loss_mult;
   m_history_trades = history;
}

//+------------------------------------------------------------------+
//| 计算手数                                                          |
//+------------------------------------------------------------------+
double CRiskManager::CalculateLotSize(double entry_price, double stop_loss, string symbol = "")
{
   if(symbol == "") symbol = _Symbol;
   
   double account_balance = GetAccountBalance();
   double risk_amount = account_balance * m_risk_percent / 100.0;
   
   double price_diff = MathAbs(entry_price - stop_loss);
   if(price_diff <= 0) return NormalizeLot(m_min_lot, symbol);
   
   double tick_value = SymbolInfoDouble(symbol, SYMBOL_TRADE_TICK_VALUE);
   double tick_size = SymbolInfoDouble(symbol, SYMBOL_TRADE_TICK_SIZE);
   
   if(tick_value <= 0 || tick_size <= 0) return NormalizeLot(m_min_lot, symbol);
   
   double lot_size = risk_amount / (price_diff / tick_size * tick_value);
   
   // 应用盈冲输缩倍数
   lot_size *= m_current_multiplier;
   
   return NormalizeLot(lot_size, symbol);
}

//+------------------------------------------------------------------+
//| 检查风险是否可接受                                                   |
//+------------------------------------------------------------------+
bool CRiskManager::IsRiskAcceptable(string symbol = "")
{
   if(symbol == "") symbol = _Symbol;
   
   double current_risk_percent = GetCurrentRiskPercent(symbol);
   return current_risk_percent < m_max_risk_percent;
}

//+------------------------------------------------------------------+
//| 检查是否可以开仓                                                     |
//+------------------------------------------------------------------+
bool CRiskManager::CanOpenPosition(ENUM_ORDER_TYPE order_type, string symbol = "")
{
   if(symbol == "") symbol = _Symbol;
   
   // 检查交易方向限制
   if(m_tradeDirection == TRADE_DIRECTION_LONG_ONLY && order_type == ORDER_TYPE_SELL)
      return false;
   if(m_tradeDirection == TRADE_DIRECTION_SHORT_ONLY && order_type == ORDER_TYPE_BUY)
      return false;
   
   // 检查开仓方向限制
   if(m_openDirection == OPEN_DIRECTION_BUY_ONLY && order_type == ORDER_TYPE_SELL)
      return false;
   if(m_openDirection == OPEN_DIRECTION_SELL_ONLY && order_type == ORDER_TYPE_BUY)
      return false;
   
   // 检查持仓数量限制
   if(GetPositionCount(symbol) >= m_maxPositions)
      return false;
   
   // 检查风险限制
   if(!IsRiskAcceptable(symbol))
      return false;
   
   return true;
}

//+------------------------------------------------------------------+
//| 获取当前风险百分比                                                   |
//+------------------------------------------------------------------+
double CRiskManager::GetCurrentRiskPercent(string symbol = "")
{
   if(symbol == "") symbol = _Symbol;
   
   double total_risk = GetTotalRisk(symbol);
   double account_balance = GetAccountBalance();
   
   if(account_balance <= 0) return 100.0;
   
   return (total_risk / account_balance) * 100.0;
}

//+------------------------------------------------------------------+
//| 更新仓位倍数（盈冲输缩）                                              |
//+------------------------------------------------------------------+
void CRiskManager::UpdatePositionMultiplier(string symbol = "")
{
   if(symbol == "") symbol = _Symbol;
   
   // 选择历史
   if(!HistorySelect(TimeCurrent() - PeriodSeconds(PERIOD_D1) * 30, TimeCurrent()))
      return;
   
   int win_count = 0;
   int total_trades = 0;
   
   // 分析最近交易
   for(int i = HistoryDealsTotal() - 1; i >= 0 && total_trades < m_history_trades; i--)
   {
      ulong ticket = HistoryDealGetTicket(i);
      if(ticket == 0) continue;
      
      if(HistoryDealGetString(ticket, DEAL_SYMBOL) != symbol) continue;
      if(m_magic_number > 0 && HistoryDealGetInteger(ticket, DEAL_MAGIC) != m_magic_number) continue;
      
      ENUM_DEAL_TYPE deal_type = (ENUM_DEAL_TYPE)HistoryDealGetInteger(ticket, DEAL_TYPE);
      if(deal_type != DEAL_TYPE_BUY && deal_type != DEAL_TYPE_SELL) continue;
      
      double profit = HistoryDealGetDouble(ticket, DEAL_PROFIT);
      
      if(profit > 0) win_count++;
      total_trades++;
   }
   
   if(total_trades == 0) return;
   
   double win_ratio = (double)win_count / total_trades;
   
   // 调整倍数
   if(win_ratio > 0.6)  // 胜率高，增加手数
   {
      m_current_multiplier = MathMin(m_current_multiplier * m_win_multiplier, 2.0);
   }
   else if(win_ratio < 0.4)  // 胜率低，减少手数
   {
      m_current_multiplier = MathMax(m_current_multiplier * m_loss_multiplier, 0.5);
   }
}

//+------------------------------------------------------------------+
//| 获取持仓数量                                                        |
//+------------------------------------------------------------------+
int CRiskManager::GetPositionCount(string symbol = "")
{
   if(symbol == "") symbol = _Symbol;
   
   int count = 0;
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) != symbol) continue;
      if(m_magic_number > 0 && PositionGetInteger(POSITION_MAGIC) != m_magic_number) continue;
      count++;
   }
   
   return count;
}

//+------------------------------------------------------------------+
//| 获取总盈利                                                          |
//+------------------------------------------------------------------+
double CRiskManager::GetTotalProfit(string symbol = "")
{
   if(symbol == "") symbol = _Symbol;
   
   double total_profit = 0;
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) != symbol) continue;
      if(m_magic_number > 0 && PositionGetInteger(POSITION_MAGIC) != m_magic_number) continue;
      
      total_profit += PositionGetDouble(POSITION_PROFIT);
   }
   
   return total_profit;
}

//+------------------------------------------------------------------+
//| 获取总风险                                                          |
//+------------------------------------------------------------------+
double CRiskManager::GetTotalRisk(string symbol = "")
{
   if(symbol == "") symbol = _Symbol;
   
   double total_risk = 0;
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) != symbol) continue;
      if(m_magic_number > 0 && PositionGetInteger(POSITION_MAGIC) != m_magic_number) continue;
      
      double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
      double stop_loss = PositionGetDouble(POSITION_SL);
      double volume = PositionGetDouble(POSITION_VOLUME);
      
      if(stop_loss > 0)
      {
         double risk = MathAbs(open_price - stop_loss) * volume;
         double tick_value = SymbolInfoDouble(symbol, SYMBOL_TRADE_TICK_VALUE);
         double tick_size = SymbolInfoDouble(symbol, SYMBOL_TRADE_TICK_SIZE);
         
         if(tick_size > 0)
            total_risk += risk / tick_size * tick_value;
      }
   }
   
   return total_risk;
}

//+------------------------------------------------------------------+
//| 标准化手数                                                          |
//+------------------------------------------------------------------+
double CRiskManager::NormalizeLot(double lot, string symbol = "")
{
   if(symbol == "") symbol = _Symbol;
   
   double min_lot = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MIN);
   double max_lot = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MAX);
   double lot_step = SymbolInfoDouble(symbol, SYMBOL_VOLUME_STEP);
   
   lot = MathMax(lot, m_min_lot);
   lot = MathMin(lot, m_max_lot);
   lot = MathMax(lot, min_lot);
   lot = MathMin(lot, max_lot);
   
   if(lot_step > 0)
      lot = MathRound(lot / lot_step) * lot_step;
   
   return lot;
}

//+------------------------------------------------------------------+
//| 获取账户余额                                                        |
//+------------------------------------------------------------------+
double CRiskManager::GetAccountBalance()
{
   return AccountInfoDouble(ACCOUNT_BALANCE);
}

//+------------------------------------------------------------------+
//| 获取账户净值                                                        |
//+------------------------------------------------------------------+
double CRiskManager::GetAccountEquity()
{
   return AccountInfoDouble(ACCOUNT_EQUITY);
}

//+------------------------------------------------------------------+
//| 基于ATR计算手数                                                     |
//+------------------------------------------------------------------+
double CRiskManager::CalculateLotSizeByATR(double entry_price, ENUM_ORDER_TYPE order_type, string symbol = "")
{
   if(symbol == "") symbol = _Symbol;
   
   double stop_loss = CalculateStopLoss(entry_price, order_type, symbol);
   return CalculateLotSize(entry_price, stop_loss, symbol);
}

//+------------------------------------------------------------------+
//| 基于ATR计算止损                                                     |
//+------------------------------------------------------------------+
double CRiskManager::CalculateStopLoss(double entry_price, ENUM_ORDER_TYPE order_type, string symbol = "")
{
   if(symbol == "") symbol = _Symbol;
   
   double atr = GetATR(m_atr_period, symbol);
   if(atr <= 0) return 0;
   
   double stop_distance = atr * m_atrMultiplier;
   double stop_loss = 0;
   
   if(order_type == ORDER_TYPE_BUY)
      stop_loss = entry_price - stop_distance;
   else if(order_type == ORDER_TYPE_SELL)
      stop_loss = entry_price + stop_distance;
   
   return NormalizeDouble(stop_loss, (int)SymbolInfoInteger(symbol, SYMBOL_DIGITS));
}

//+------------------------------------------------------------------+
//| 计算止盈                                                          |
//+------------------------------------------------------------------+
double CRiskManager::CalculateTakeProfit(double entry_price, double stop_loss, string symbol = "")
{
   if(symbol == "") symbol = _Symbol;
   
   double stop_distance = MathAbs(entry_price - stop_loss);
   double profit_distance = stop_distance * m_riskRewardRatio;
   double take_profit = 0;
   
   if(entry_price > stop_loss) // 做多
      take_profit = entry_price + profit_distance;
   else // 做空
      take_profit = entry_price - profit_distance;
   
   return NormalizeDouble(take_profit, (int)SymbolInfoInteger(symbol, SYMBOL_DIGITS));
}

//+------------------------------------------------------------------+
//| 获取ATR值                                                         |
//+------------------------------------------------------------------+
double CRiskManager::GetATR(int period, string symbol = "")
{
   if(symbol == "") symbol = _Symbol;
   
   double atr_buffer[];
   int atr_handle = iATR(symbol, PERIOD_CURRENT, period);
   
   if(atr_handle == INVALID_HANDLE) return 0;
   
   if(CopyBuffer(atr_handle, 0, 0, 1, atr_buffer) <= 0)
   {
      IndicatorRelease(atr_handle);
      return 0;
   }
   
   IndicatorRelease(atr_handle);
   return atr_buffer[0];
}
