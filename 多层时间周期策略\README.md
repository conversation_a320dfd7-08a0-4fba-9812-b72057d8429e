# 日内多时间周期交易EA

## 概述

这是一个基于多时间周期分析的日内交易EA，严格按照您提供的日内交易手册实现。EA采用1小时图判断方向、15分钟图确认回调、5分钟图精准入场的策略。

## 交易哲学

- **跟随1小时级别的日内主要方向**
- **不错过任何一次顺势的短线机会**
- **积小胜为大胜，资金利用率高**
- **不持仓过夜，持仓周期最多1小时**
- **未往预期方向走结构的话不要管盈亏直接走**

## 文件结构

```
多层时间周期策略/
├── DayTradeMultiTimeframe.mq5          # 主EA文件
├── MultiTimeframeAnalyzer.mqh          # 多时间周期分析器
├── DayTradeSignalModule.mqh            # 信号模块
├── DayTradeRiskManager.mqh             # 风险管理模块
├── DayTradePositionManager.mqh         # 仓位管理模块
└── README.md                           # 说明文档
```

## 核心模块

### 1. 多时间周期分析器 (MultiTimeframeAnalyzer.mqh)
- **锚定时间周期 (1H)**: 判断主要趋势方向
- **设伏时间周期 (15M)**: 确认健康回调和恢复信号
- **触发时间周期 (5M)**: 精准入场时机

### 2. 信号模块 (DayTradeSignalModule.mqh)
- **EMA20突破检测**: 5分钟图价格突破EMA20
- **成交量确认**: 突破伴随成交量放大
- **标志性阳线**: 强势K线形态确认

### 3. 风险管理模块 (DayTradeRiskManager.mqh)
- **动态仓位计算**: 基于ATR和风险百分比
- **智能止损设置**: ATR倍数止损
- **风险回报比控制**: 可配置止盈风险比

### 4. 仓位管理模块 (DayTradePositionManager.mqh)
- **单一持仓策略**: 同时只持有一个仓位
- **时间控制**: 最大持仓时间限制
- **自动平仓**: 交易时间结束强制平仓

## 日内交易Checklist实现

### A. 日内方向 (1小时图)
✅ **检查项目1**: 1小时图的趋势是否明确向上？
- 价格在EMA20之上
- EMA呈上升趋势
- 趋势强度达到阈值

### B. 战术设伏 (15分钟图)
✅ **检查项目2**: 15分钟图价格是否正处于对1小时趋势的健康回调中？
- 回调至15分钟EMA20附近或关键支撑位
- 回调幅度在合理范围内(最大61.8%)

✅ **检查项目3**: 15分钟图上是否已出现回调结束的初步信号？
- 止跌企稳信号
- 形成小平台
- 最低价不再创新低

### C. 精准入场 (5分钟图)
✅ **检查项目4**: 5分钟图价格是否明确地突破了其自身的EMA20？
- 当前价格在EMA20之上
- 前一根K线在EMA20之下或接近
- 突破幅度足够(可配置最小点数)

✅ **检查项目5**: 5分钟图的突破是否得到了成交量放大或标志性阳线的支持？
- 成交量放大确认(可配置倍数)
- 标志性阳线形态
- 突破有效性验证

## 参数配置

### 基础设置
- **EA魔术号**: 区分不同EA的订单
- **交易备注**: 订单备注信息

### 时间周期设置
- **锚定时间周期**: 默认1小时(PERIOD_H1)
- **设伏时间周期**: 默认15分钟(PERIOD_M15)  
- **触发时间周期**: 默认5分钟(PERIOD_M5)

### EMA均线参数
- **EMA周期**: 默认20
- **均线计算方法**: EMA
- **应用价格**: 收盘价

### 趋势确认参数
- **趋势强度阈值**: 0.7 (70%的K线呈上升趋势)
- **最小趋势确认K线数**: 3根
- **最大回调百分比**: 0.618 (61.8%)

### 突破确认参数
- **最小突破点数**: 5.0点
- **成交量放大倍数**: 1.2倍
- **要求标志性阳线**: 是

### 风险管理参数
- **单笔风险百分比**: 1.0%
- **止损ATR倍数**: 2.0
- **止盈风险比**: 2.0 (1:2)
- **最大持仓时间**: 60分钟

### 交易时间控制
- **开始交易时间**: "09:00"
- **结束交易时间**: "15:00"
- **收盘时强制平仓**: 是

## 使用方法

### 1. 安装步骤
1. 将所有文件复制到MT5的`MQL5/Experts/多层时间周期策略/`目录
2. 在MetaEditor中编译`DayTradeMultiTimeframe.mq5`
3. 在MT5中添加EA到图表

### 2. 推荐设置
- **图表周期**: 5分钟图(用于监控入场信号)
- **品种选择**: 流动性好的主要货币对
- **账户类型**: 标准账户或ECN账户
- **最小资金**: 建议1000美元以上

### 3. 风险提示
- 日内交易风险较高，请合理控制仓位
- 建议先在模拟账户测试
- 注意交易时间设置，避免重要新闻时段
- 定期检查EA运行状态

## 特色功能

### 1. 完整的Checklist验证
- 严格按照交易手册的5个检查项目执行
- 任何一项不满足都不会开仓
- 详细的日志输出，便于分析

### 2. 智能风险管理
- 基于ATR的动态止损
- 账户风险百分比控制
- 自动计算最优仓位大小

### 3. 时间管理
- 交易时间窗口控制
- 最大持仓时间限制
- 强制平仓保护

### 4. 模块化设计
- 清晰的代码结构
- 易于维护和扩展
- 详细的注释说明

## 性能监控

EA会输出详细的日志信息，包括：
- Checklist检查结果
- 开仓/平仓操作
- 风险计算详情
- 持仓状态更新

## 技术支持

如有问题或需要定制，请联系开发团队。

---

**免责声明**: 外汇交易存在高风险，可能导致资金损失。请在充分了解风险的情况下使用本EA。
