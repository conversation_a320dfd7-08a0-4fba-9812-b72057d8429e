//+------------------------------------------------------------------+
//|                                          EntrySignalModule.mqh |
//|                                  Copyright 2024, TradingSystem |
//|                                             入场信号模块        |
//+------------------------------------------------------------------+

#ifndef ENTRY_SIGNAL_MODULE_H
#define ENTRY_SIGNAL_MODULE_H

//--- 信号类型枚举
enum ENUM_SIGNAL_TYPE
{
    SIGNAL_NONE,        // 无信号
    SIGNAL_BUY,         // 买入信号
    SIGNAL_SELL         // 卖出信号
};

//+------------------------------------------------------------------+
//| 入场信号模块类                                                   |
//+------------------------------------------------------------------+
class CEntrySignalModule
{
private:
    // 唐奇安通道
    int m_donchianPeriod;
    double m_donchianHigh[];
    double m_donchianLow[];
    bool m_enableDonchianBreakout;
    
    // 波动性突破
    int m_emaHandle;
    int m_emaPeriod;
    int m_atrHandle;
    int m_atrPeriod;
    double m_breakoutATRMultiplier;
    bool m_enableEMABreakout;
    bool m_enableATRFilter;
    
    // 当前信号
    ENUM_SIGNAL_TYPE m_currentSignal;
    
    // 私有方法
    void CalculateDonchianChannel();
    ENUM_SIGNAL_TYPE CheckDonchianBreakout();
    ENUM_SIGNAL_TYPE CheckVolatilityBreakout();
    bool ValidateSignalWithATR();
    
public:
    CEntrySignalModule();
    ~CEntrySignalModule();
    
    bool Init(int donchianPeriod, int emaPeriod, double breakoutATRMultiplier, int atrPeriod,
              bool enableDonchianBreakout, bool enableEMABreakout, bool enableATRFilter);
    void Update();
    ENUM_SIGNAL_TYPE GetEntrySignal() const { return m_currentSignal; }
    
    // 获取具体数值
    double GetDonchianHigh(int shift = 0);
    double GetDonchianLow(int shift = 0);
    double GetBreakoutLevel(bool isUpper);
};

//+------------------------------------------------------------------+
//| 构造函数                                                         |
//+------------------------------------------------------------------+
CEntrySignalModule::CEntrySignalModule()
{
    m_emaHandle = INVALID_HANDLE;
    m_atrHandle = INVALID_HANDLE;
    m_currentSignal = SIGNAL_NONE;
    
    ArraySetAsSeries(m_donchianHigh, true);
    ArraySetAsSeries(m_donchianLow, true);
}

//+------------------------------------------------------------------+
//| 析构函数                                                         |
//+------------------------------------------------------------------+
CEntrySignalModule::~CEntrySignalModule()
{
    if(m_emaHandle != INVALID_HANDLE)
        IndicatorRelease(m_emaHandle);
    if(m_atrHandle != INVALID_HANDLE)
        IndicatorRelease(m_atrHandle);
}

//+------------------------------------------------------------------+
//| 初始化函数                                                       |
//+------------------------------------------------------------------+
bool CEntrySignalModule::Init(int donchianPeriod, int emaPeriod, 
                              double breakoutATRMultiplier, int atrPeriod,
                              bool enableDonchianBreakout, bool enableEMABreakout, bool enableATRFilter)
{
    m_donchianPeriod = donchianPeriod;
    m_emaPeriod = emaPeriod;
    m_breakoutATRMultiplier = breakoutATRMultiplier;
    m_atrPeriod = atrPeriod;
    m_enableDonchianBreakout = enableDonchianBreakout;
    m_enableEMABreakout = enableEMABreakout;
    m_enableATRFilter = enableATRFilter;
    
    // 创建EMA指标（如果启用EMA突破或ATR过滤）
    if(m_enableEMABreakout || m_enableATRFilter)
    {
        m_emaHandle = iMA(_Symbol, PERIOD_CURRENT, m_emaPeriod, 0, MODE_EMA, PRICE_CLOSE);
        if(m_emaHandle == INVALID_HANDLE)
        {
            Print("EMA指标创建失败");
            return false;
        }
    }
    
    // 创建ATR指标（如果启用ATR过滤或EMA突破）
    if(m_enableATRFilter || m_enableEMABreakout)
    {
        m_atrHandle = iATR(_Symbol, PERIOD_CURRENT, m_atrPeriod);
        if(m_atrHandle == INVALID_HANDLE)
        {
            Print("ATR指标创建失败");
            return false;
        }
    }
    
    // 初始化唐奇安通道数组（如果启用唐奇安突破）
    if(m_enableDonchianBreakout)
    {
        ArrayResize(m_donchianHigh, m_donchianPeriod + 10);
        ArrayResize(m_donchianLow, m_donchianPeriod + 10);
    }
    
    Print("入场信号模块初始化成功 - 唐奇安:", (m_enableDonchianBreakout ? "启用" : "禁用"),
          " EMA突破:", (m_enableEMABreakout ? "启用" : "禁用"),
          " ATR过滤:", (m_enableATRFilter ? "启用" : "禁用"));
    return true;
}

//+------------------------------------------------------------------+
//| 更新函数                                                         |
//+------------------------------------------------------------------+
void CEntrySignalModule::Update()
{
    ENUM_SIGNAL_TYPE donchianSignal = SIGNAL_NONE;
    ENUM_SIGNAL_TYPE volatilitySignal = SIGNAL_NONE;
    
    // 检查唐奇安突破信号（如果启用）
    if(m_enableDonchianBreakout)
    {
        CalculateDonchianChannel();
        donchianSignal = CheckDonchianBreakout();
    }
    
    // 检查波动性突破信号（如果启用）
    if(m_enableEMABreakout)
    {
        volatilitySignal = CheckVolatilityBreakout();
    }
    
    // 综合判断信号（任一启用的信号触发即可）
    if(donchianSignal != SIGNAL_NONE)
        m_currentSignal = donchianSignal;
    else if(volatilitySignal != SIGNAL_NONE)
        m_currentSignal = volatilitySignal;
    else
        m_currentSignal = SIGNAL_NONE;
        
    // 如果启用ATR过滤器，需要额外验证信号强度
    if(m_currentSignal != SIGNAL_NONE && m_enableATRFilter)
    {
        if(!ValidateSignalWithATR())
            m_currentSignal = SIGNAL_NONE;
    }
}

//+------------------------------------------------------------------+
//| 使用ATR验证信号强度                                              |
//+------------------------------------------------------------------+
bool CEntrySignalModule::ValidateSignalWithATR()
{
    double atr[];
    if(CopyBuffer(m_atrHandle, 0, 0, 2, atr) <= 0)
        return false;
        
    // 当前ATR应该大于前一根K线的ATR，表示波动率在增加
    return (atr[0] > atr[1]);
}

//+------------------------------------------------------------------+
//| 计算唐奇安通道                                                   |
//+------------------------------------------------------------------+
void CEntrySignalModule::CalculateDonchianChannel()
{
    int bars = m_donchianPeriod + 10;
    
    // 获取足够的K线数据
    double high[], low[];
    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    
    if(CopyHigh(_Symbol, PERIOD_CURRENT, 0, bars, high) <= 0 ||
       CopyLow(_Symbol, PERIOD_CURRENT, 0, bars, low) <= 0)
    {
        Print("获取价格数据失败");
        return;
    }
    
    // 计算每个位置的唐奇安通道值
    for(int i = 0; i < bars - m_donchianPeriod; i++)
    {
        double maxHigh = high[ArrayMaximum(high, i + 1, m_donchianPeriod)];
        double minLow = low[ArrayMinimum(low, i + 1, m_donchianPeriod)];
        
        m_donchianHigh[i] = maxHigh;
        m_donchianLow[i] = minLow;
    }
}

//+------------------------------------------------------------------+
//| 检查唐奇安突破                                                   |
//+------------------------------------------------------------------+
ENUM_SIGNAL_TYPE CEntrySignalModule::CheckDonchianBreakout()
{
    double close[];
    ArraySetAsSeries(close, true);
    
    if(CopyClose(_Symbol, PERIOD_CURRENT, 0, 3, close) <= 0)
        return SIGNAL_NONE;
    
    // 当前收盘价突破过去N周期最高价
    if(close[0] > m_donchianHigh[1])
        return SIGNAL_BUY;
    
    // 当前收盘价跌破过去N周期最低价
    if(close[0] < m_donchianLow[1])
        return SIGNAL_SELL;
    
    return SIGNAL_NONE;
}

//+------------------------------------------------------------------+
//| 检查波动性突破                                                   |
//+------------------------------------------------------------------+
ENUM_SIGNAL_TYPE CEntrySignalModule::CheckVolatilityBreakout()
{
    double close[], ema[], atr[];
    ArraySetAsSeries(close, true);
    ArraySetAsSeries(ema, true);
    ArraySetAsSeries(atr, true);
    
    if(CopyClose(_Symbol, PERIOD_CURRENT, 0, 2, close) <= 0 ||
       CopyBuffer(m_emaHandle, 0, 0, 2, ema) <= 0 ||
       CopyBuffer(m_atrHandle, 0, 0, 2, atr) <= 0)
    {
        return SIGNAL_NONE;
    }
    
    // 计算突破阈值
    double upperBreakout = ema[0] + m_breakoutATRMultiplier * atr[0];
    double lowerBreakout = ema[0] - m_breakoutATRMultiplier * atr[0];
    
    // 收盘价突破上轨
    if(close[0] > upperBreakout)
        return SIGNAL_BUY;
    
    // 收盘价跌破下轨
    if(close[0] < lowerBreakout)
        return SIGNAL_SELL;
    
    return SIGNAL_NONE;
}

//+------------------------------------------------------------------+
//| 获取唐奇安通道上轨                                               |
//+------------------------------------------------------------------+
double CEntrySignalModule::GetDonchianHigh(int shift = 0)
{
    if(shift >= ArraySize(m_donchianHigh))
        return 0.0;
    return m_donchianHigh[shift];
}

//+------------------------------------------------------------------+
//| 获取唐奇安通道下轨                                               |
//+------------------------------------------------------------------+
double CEntrySignalModule::GetDonchianLow(int shift = 0)
{
    if(shift >= ArraySize(m_donchianLow))
        return 0.0;
    return m_donchianLow[shift];
}

//+------------------------------------------------------------------+
//| 获取波动性突破水平                                               |
//+------------------------------------------------------------------+
double CEntrySignalModule::GetBreakoutLevel(bool isUpper)
{
    double ema[], atr[];
    
    if(CopyBuffer(m_emaHandle, 0, 0, 1, ema) <= 0 ||
       CopyBuffer(m_atrHandle, 0, 0, 1, atr) <= 0)
    {
        return 0.0;
    }
    
    if(isUpper)
        return ema[0] + m_breakoutATRMultiplier * atr[0];
    else
        return ema[0] - m_breakoutATRMultiplier * atr[0];
}

#endif // ENTRY_SIGNAL_MODULE_H
