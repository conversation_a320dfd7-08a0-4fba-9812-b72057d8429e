//+------------------------------------------------------------------+
//|                                              TestCompilation.mq5 |
//|                                  Copyright 2024, TradingSystem |
//|                                    编译测试文件                 |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, TradingSystem"
#property link      ""
#property version   "1.00"
#property description "用于测试所有模块是否能正确编译"

// 测试所有模块的包含
#include "MultiTimeframeAnalyzer.mqh"
#include "DayTradeSignalModule.mqh"
#include "DayTradeRiskManager.mqh"
#include "DayTradePositionManager.mqh"

// 模拟主EA中的输入参数
input int InpEMA_Period = 20;
input double InpTrendStrengthThreshold = 0.7;
input int InpMinTrendBars = 3;
input double InpPullbackMaxPercent = 0.618;
input double InpBreakoutMinPips = 5.0;
input double InpVolumeMultiplier = 1.2;
input bool InpRequireStrongCandle = true;
input string InpTradeComment = "TestCompile";

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("=== 编译测试开始 ===");
    
    // 测试创建所有模块实例
    CMultiTimeframeAnalyzer* testMTF = new CMultiTimeframeAnalyzer();
    CDayTradeSignalModule* testSignal = new CDayTradeSignalModule();
    CDayTradeRiskManager* testRisk = new CDayTradeRiskManager();
    CDayTradePositionManager* testPosition = new CDayTradePositionManager();
    
    if(testMTF != NULL && testSignal != NULL && testRisk != NULL && testPosition != NULL)
    {
        Print("✅ 所有模块实例创建成功");
        
        // 测试初始化
        bool mtfInit = testMTF.Initialize(PERIOD_H1, PERIOD_M15, PERIOD_M5);
        bool signalInit = testSignal.Initialize(20, 5.0, 1.2);
        bool riskInit = testRisk.Initialize(1.0, 2.0, 2.0);
        bool positionInit = testPosition.Initialize(123456, 60);
        
        if(mtfInit && signalInit && riskInit && positionInit)
        {
            Print("✅ 所有模块初始化成功");
        }
        else
        {
            Print("❌ 部分模块初始化失败");
        }
    }
    else
    {
        Print("❌ 模块实例创建失败");
    }
    
    // 清理资源
    if(testMTF != NULL) delete testMTF;
    if(testSignal != NULL) delete testSignal;
    if(testRisk != NULL) delete testRisk;
    if(testPosition != NULL) delete testPosition;
    
    Print("=== 编译测试完成 ===");
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("编译测试EA已关闭");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // 测试用，不执行任何操作
}
