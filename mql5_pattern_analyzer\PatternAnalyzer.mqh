//+------------------------------------------------------------------+
//|                                              PatternAnalyzer.mqh |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"

//+------------------------------------------------------------------+
//| 信号强度枚举                                                        |
//+------------------------------------------------------------------+
enum ENUM_SIGNAL_STRENGTH
{
   SIGNAL_WEAK,     // 弱信号
   SIGNAL_MEDIUM,   // 中等信号
   SIGNAL_HIGH      // 强信号
};

//+------------------------------------------------------------------+
//| 信号类型枚举                                                        |
//+------------------------------------------------------------------+
enum ENUM_SIGNAL_TYPE
{
   SIGNAL_TYPE_A,   // 完美形态
   SIGNAL_TYPE_B,   // 趋势突破
   SIGNAL_TYPE_C,   // 形态突破确认
   SIGNAL_TYPE_D    // 其他交易机会
};

//+------------------------------------------------------------------+
//| 形态分析结果结构                                                      |
//+------------------------------------------------------------------+
struct PatternResult
{
   bool              is_valid;      // 是否有效
   string            info;          // 详细信息
   double            confidence;    // 置信度 0-1
};

//+------------------------------------------------------------------+
//| 交易信号结构                                                        |
//+------------------------------------------------------------------+
struct TradingSignal
{
   ENUM_SIGNAL_TYPE  type;          // 信号类型
   ENUM_SIGNAL_STRENGTH strength;   // 信号强度
   string            details;       // 详细描述
   double            entry_price;   // 建议入场价
   double            stop_loss;     // 止损价
   double            take_profit;   // 止盈价
};

//+------------------------------------------------------------------+
//| 技术形态分析器类                                                      |
//+------------------------------------------------------------------+
class CPatternAnalyzer
{
private:
   int               m_min_pattern_bars;
   double            m_error_margin;
   int               m_pullback_bars;
   
public:
   CPatternAnalyzer(void);
   CPatternAnalyzer(int min_bars, double error_margin, int pullback_bars);
   ~CPatternAnalyzer(void);
   
   // 核心分析方法
   PatternResult     AnalyzeTrendLineBreakout(const double &high[], const double &low[], const double &close[], int size);
   PatternResult     AnalyzeSupportBoxBreakout(const double &close[], int size);
   PatternResult     AnalyzeVolumeSurge(const double &open[], const double &close[], const long &volume[], int size);
   PatternResult     AnalyzeConvergingTriangle(const double &high[], const double &low[], int size);
   PatternResult     AnalyzeKLinePattern(const double &open[], const double &close[], const double &high[], const double &low[], const long &volume[], int size);
   
   // 综合分析
   int               AnalyzeAllPatterns(const double &open[], const double &high[], const double &low[], const double &close[], const long &volume[], int size, PatternResult &results[]);
   int               DetectTradingSignals(const double &open[], const double &high[], const double &low[], const double &close[], const long &volume[], int size, TradingSignal &signals[]);
   
   // 辅助方法
   double            CalculateLinearRegression(const double &x[], const double &y[], int size, double &slope, double &intercept, double &r_squared);
   void              FindLocalExtrema(const double &data[], int size, int window, int &peaks[], int &peak_count, int &troughs[], int &trough_count);
};

//+------------------------------------------------------------------+
//| 构造函数                                                           |
//+------------------------------------------------------------------+
CPatternAnalyzer::CPatternAnalyzer(void)
{
   m_min_pattern_bars = 15;
   m_error_margin = 0.001;
   m_pullback_bars = 3;
}

CPatternAnalyzer::CPatternAnalyzer(int min_bars, double error_margin, int pullback_bars)
{
   m_min_pattern_bars = min_bars;
   m_error_margin = error_margin;
   m_pullback_bars = pullback_bars;
}

CPatternAnalyzer::~CPatternAnalyzer(void) {}

//+------------------------------------------------------------------+
//| 线性回归计算                                                        |
//+------------------------------------------------------------------+
double CPatternAnalyzer::CalculateLinearRegression(const double &x[], const double &y[], int size, double &slope, double &intercept, double &r_squared)
{
   if(size < 2) { slope = 0; intercept = 0; r_squared = 0; return 0; }
   
   double sum_x = 0, sum_y = 0, sum_xy = 0, sum_x2 = 0, sum_y2 = 0;
   for(int i = 0; i < size; i++)
   {
      sum_x += x[i]; sum_y += y[i]; sum_xy += x[i] * y[i];
      sum_x2 += x[i] * x[i]; sum_y2 += y[i] * y[i];
   }
   
   double n = (double)size;
   double denominator = n * sum_x2 - sum_x * sum_x;
   if(MathAbs(denominator) < 1e-10) { slope = 0; intercept = sum_y / n; r_squared = 0; return intercept; }
   
   slope = (n * sum_xy - sum_x * sum_y) / denominator;
   intercept = (sum_y - slope * sum_x) / n;
   
   double ss_tot = sum_y2 - (sum_y * sum_y) / n;
   double ss_res = 0;
   for(int i = 0; i < size; i++)
   {
      double predicted = slope * x[i] + intercept;
      ss_res += (y[i] - predicted) * (y[i] - predicted);
   }
   r_squared = (ss_tot > 0) ? (1.0 - ss_res / ss_tot) : 0;
   return slope;
}

//+------------------------------------------------------------------+
//| 寻找局部极值                                                        |
//+------------------------------------------------------------------+
void CPatternAnalyzer::FindLocalExtrema(const double &data[], int size, int window, int &peaks[], int &peak_count, int &troughs[], int &trough_count)
{
   peak_count = 0; trough_count = 0;
   for(int i = window; i < size - window; i++)
   {
      bool is_peak = true, is_trough = true;
      for(int j = i - window; j <= i + window; j++)
      {
         if(j != i && data[j] >= data[i]) is_peak = false;
         if(j != i && data[j] <= data[i]) is_trough = false;
      }
      if(is_peak && peak_count < 20) peaks[peak_count++] = i;
      if(is_trough && trough_count < 20) troughs[trough_count++] = i;
   }
}

//+------------------------------------------------------------------+
//| 趋势线突破分析                                                       |
//+------------------------------------------------------------------+
PatternResult CPatternAnalyzer::AnalyzeTrendLineBreakout(const double &high[], const double &low[], const double &close[], int size)
{
   PatternResult result; result.is_valid = false; result.confidence = 0.0;
   if(size < 20) { result.info = "数据不足"; return result; }
   
   int recent_size = MathMin(20, size);
   double x_data[], y_data[];
   ArrayResize(x_data, recent_size); ArrayResize(y_data, recent_size);
   
   for(int i = 0; i < recent_size; i++)
   {
      x_data[i] = i;
      y_data[i] = low[size - recent_size + i];
   }
   
   double slope, intercept, r_squared;
   CalculateLinearRegression(x_data, y_data, recent_size, slope, intercept, r_squared);
   
   double trend_value = slope * (recent_size - 1) + intercept;
   double current_price = close[size - 1];
   
   if(current_price > trend_value && slope > 0 && r_squared > -0.5)
   {
      result.is_valid = true;
      result.confidence = MathMin(0.9, r_squared + 0.3);
      result.info = StringFormat("趋势线突破: 价格=%.5f, 趋势值=%.5f", current_price, trend_value);
   }
   return result;
}

//+------------------------------------------------------------------+
//| 箱体突破分析                                                        |
//+------------------------------------------------------------------+
PatternResult CPatternAnalyzer::AnalyzeSupportBoxBreakout(const double &close[], int size)
{
   PatternResult result; result.is_valid = false; result.confidence = 0.0;
   if(size < 50) { result.info = "数据不足"; return result; }
   
   int recent_size = MathMin(50, size);
   double prices[]; ArrayResize(prices, recent_size);
   
   for(int i = 0; i < recent_size; i++)
      prices[i] = close[size - recent_size + i];
   
   ArraySort(prices);
   double box_bottom = prices[(int)(recent_size * 0.3)];
   double box_top = prices[(int)(recent_size * 0.7)];
   double latest_close = close[size - 1];
   
   if(latest_close > box_top * (1 + m_error_margin))
   {
      result.is_valid = true;
      result.confidence = 0.8;
      result.info = StringFormat("箱体突破: 价格=%.5f, 箱体=%.5f-%.5f", latest_close, box_bottom, box_top);
   }
   return result;
}

//+------------------------------------------------------------------+
//| 成交量放大分析                                                       |
//+------------------------------------------------------------------+
PatternResult CPatternAnalyzer::AnalyzeVolumeSurge(const double &open[], const double &close[], const long &volume[], int size)
{
   PatternResult result; result.is_valid = false; result.confidence = 0.0;
   if(size < 6) { result.info = "数据不足"; return result; }
   
   if(close[size - 1] <= open[size - 1]) { result.info = "非阳线"; return result; }
   
   long latest_volume = volume[size - 1];
   long sum_volume = 0;
   for(int i = size - 6; i < size - 1; i++) sum_volume += volume[i];
   double avg_volume = (double)sum_volume / 5.0;
   double volume_ratio = (double)latest_volume / avg_volume;
   
   if(volume_ratio > 1.5)
   {
      result.is_valid = true;
      result.confidence = MathMin(0.9, volume_ratio / 3.0);
      result.info = StringFormat("放量阳线: 量比=%.2f", volume_ratio);
   }
   return result;
}

//+------------------------------------------------------------------+
//| 收敛三角形分析                                                       |
//+------------------------------------------------------------------+
PatternResult CPatternAnalyzer::AnalyzeConvergingTriangle(const double &high[], const double &low[], int size)
{
   PatternResult result; result.is_valid = false; result.confidence = 0.0;
   if(size < 30) { result.info = "数据不足"; return result; }
   
   int recent_size = MathMin(30, size);
   double highs[], lows[];
   ArrayResize(highs, recent_size); ArrayResize(lows, recent_size);
   
   for(int i = 0; i < recent_size; i++)
   {
      highs[i] = high[size - recent_size + i];
      lows[i] = low[size - recent_size + i];
   }
   
   int peaks[20], troughs[20], peak_count, trough_count;
   FindLocalExtrema(highs, recent_size, 3, peaks, peak_count, troughs, trough_count);
   
   if(peak_count >= 3 && trough_count >= 3)
   {
      result.is_valid = true;
      result.confidence = 0.7;
      result.info = StringFormat("收敛三角形: 高点=%d, 低点=%d", peak_count, trough_count);
   }
   return result;
}

//+------------------------------------------------------------------+
//| K线组合分析                                                        |
//+------------------------------------------------------------------+
PatternResult CPatternAnalyzer::AnalyzeKLinePattern(const double &open[], const double &close[], const double &high[], const double &low[], const long &volume[], int size)
{
   PatternResult result; result.is_valid = false; result.confidence = 0.0;
   if(size < 5) { result.info = "数据不足"; return result; }
   
   int latest = size - 1;
   double body = close[latest] - open[latest];
   if(body <= 0) { result.info = "非阳线"; return result; }
   
   double body_ratio = body / (high[latest] - low[latest]);
   long sum_volume = 0;
   for(int i = size - 5; i < size - 1; i++) sum_volume += volume[i];
   double volume_ratio = (double)volume[latest] / ((double)sum_volume / 4.0);
   
   if(body_ratio > 0.6 && volume_ratio > 1.2)
   {
      result.is_valid = true;
      result.confidence = 0.8;
      result.info = StringFormat("大阳线: 实体比=%.2f, 量比=%.2f", body_ratio, volume_ratio);
   }
   return result;
}

//+------------------------------------------------------------------+
//| 综合分析                                                          |
//+------------------------------------------------------------------+
int CPatternAnalyzer::AnalyzeAllPatterns(const double &open[], const double &high[], const double &low[], const double &close[], const long &volume[], int size, PatternResult &results[])
{
   ArrayResize(results, 5);
   results[0] = AnalyzeTrendLineBreakout(high, low, close, size);
   results[1] = AnalyzeSupportBoxBreakout(close, size);
   results[2] = AnalyzeVolumeSurge(open, close, volume, size);
   results[3] = AnalyzeConvergingTriangle(high, low, size);
   results[4] = AnalyzeKLinePattern(open, close, high, low, volume, size);
   return 5;
}

//+------------------------------------------------------------------+
//| 检测交易信号                                                        |
//+------------------------------------------------------------------+
int CPatternAnalyzer::DetectTradingSignals(const double &open[], const double &high[], const double &low[], const double &close[], const long &volume[], int size, TradingSignal &signals[])
{
   PatternResult patterns[5];
   AnalyzeAllPatterns(open, high, low, close, volume, size, patterns);
   
   int signal_count = 0;
   bool trend_break = patterns[0].is_valid;
   bool box_break = patterns[1].is_valid;
   bool volume_surge = patterns[2].is_valid;
   bool triangle = patterns[3].is_valid;
   bool k_line = patterns[4].is_valid;
   
   // A类信号：完美形态
   if(trend_break && box_break && volume_surge && (triangle || k_line))
   {
      signals[signal_count].type = SIGNAL_TYPE_A;
      signals[signal_count].strength = SIGNAL_HIGH;
      signals[signal_count].details = "完美形态：多重确认";
      signals[signal_count].entry_price = close[size - 1];
      signal_count++;
   }
   // B类信号：趋势突破
   else if(trend_break && (box_break || volume_surge || k_line))
   {
      signals[signal_count].type = SIGNAL_TYPE_B;
      signals[signal_count].strength = SIGNAL_MEDIUM;
      signals[signal_count].details = "趋势突破：有确认";
      signals[signal_count].entry_price = close[size - 1];
      signal_count++;
   }
   // C类信号：形态突破
   else if((box_break || triangle) && (volume_surge || k_line))
   {
      signals[signal_count].type = SIGNAL_TYPE_C;
      signals[signal_count].strength = SIGNAL_MEDIUM;
      signals[signal_count].details = "形态突破：有确认";
      signals[signal_count].entry_price = close[size - 1];
      signal_count++;
   }
   
   return signal_count;
}