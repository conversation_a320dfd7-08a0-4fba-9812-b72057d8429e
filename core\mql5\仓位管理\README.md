# MT5 一键交易与风险管理 EA 使用说明

## 项目概述

这是一个专业的MetaTrader 5 (MT5) 智能交易系统 (EA)，提供一键交易功能和专业的风险管理。EA会根据ATR指标动态计算仓位大小、止损和止盈，帮助交易者实现科学的风险控制。

## 文件结构

```
仓位管理/
├── OneClickTrader.mq5                    # 主EA文件
├── Include/
│   ├── OneClickTrader_Core.mqh          # 核心交易逻辑
│   ├── OneClickTrader_Calculator.mqh    # 仓位计算器
│   └── OneClickTrader_UI.mqh            # 用户界面管理
└── README.md                            # 使用说明
```

## 主要功能

### 1. 智能仓位计算
- **基于账户净值**: 所有风险计算基于实时账户净值
- **ATR动态止损**: 使用ATR指标计算动态止损距离
- **风险回报比**: 根据设定的风险回报比自动计算止盈
- **通用性**: 自动适配所有交易品种（外汇、黄金、指数、加密货币等）

### 2. 三档风险管理
- **试仓模式**: 低风险交易，适合测试策略
- **标准仓模式**: 常规风险水平，日常交易使用
- **重仓模式**: 高风险高收益，适合高确定性机会

### 3. 一键交易执行
- **BUY按钮**: 一键执行买入交易
- **SELL按钮**: 一键执行卖出交易
- **实时计算**: 点击前实时显示所有交易参数

### 4. 专业UI界面
- **信息显示区**: 实时显示计算结果
- **操作控制区**: 风险档位选择和交易执行按钮
- **可定制**: 支持位置和颜色自定义

## 输入参数说明

### 风险管理设置
- `Trial_Risk_Percent`: 试仓风险百分比 (默认: 0.2%)
- `Standard_Risk_Percent`: 标准仓风险百分比 (默认: 0.5%)
- `Heavy_Risk_Percent`: 重仓风险百分比 (默认: 2.0%)

### 技术指标设置
- `ATR_Period`: ATR周期 (默认: 14)
- `ATR_Multiplier`: ATR乘数 (默认: 2.0)
- `Risk_Reward_Ratio`: 风险回报比 (默认: 2.0，即1:2)

### 交易设置
- `Magic_Number`: 魔术手号码 (默认: 888888)
- `Slippage`: 允许滑点 (默认: 3点)

### 界面设置
- `Panel_Corner`: UI面板位置
- `Panel_Background`: 面板背景色
- `Button_Active`: 激活按钮颜色
- `Button_Inactive`: 非激活按钮颜色
- `Text_Color`: 文字颜色

## 使用步骤

### 1. 安装EA
1. 将整个`仓位管理`文件夹复制到MT5的`MQL5/Experts/`目录下
2. 在MT5中编译`OneClickTrader.mq5`文件
3. 将EA拖拽到图表上

### 2. 参数配置
1. 根据个人风险偏好设置三档风险百分比
2. 调整ATR参数以适应不同市场环境
3. 设置合适的风险回报比
4. 自定义UI界面位置和颜色

### 3. 日常使用
1. **选择风险档位**: 点击"试仓"、"标准仓"或"重仓"按钮
2. **查看计算结果**: 观察信息显示区的实时数据
3. **执行交易**: 点击"BUY"或"SELL"按钮一键下单

## 计算逻辑详解

### 仓位大小计算
```
风险金额 = 账户净值 × 风险百分比
止损距离 = ATR值 × ATR乘数
仓位大小 = 风险金额 ÷ (止损距离 × 点值)
```

### 止损止盈设置
```
止损价格 = 当前价格 ± 止损距离
止盈距离 = 止损距离 × 风险回报比
止盈价格 = 当前价格 ± 止盈距离
```

## 安全特性

### 1. 参数验证
- 自动检查手数是否在允许范围内
- 验证保证金是否充足
- 确保止损止盈价格合理

### 2. 错误处理
- 详细的错误日志记录
- 交易失败时的明确提示
- 网络断线等异常情况的处理

### 3. 资源管理
- 正确释放指标句柄
- 清理所有UI对象
- 避免内存泄漏

## 注意事项

1. **市场时间**: 确保在市场开放时间使用
2. **网络连接**: 保持稳定的网络连接
3. **资金管理**: 合理设置风险百分比，避免过度杠杆
4. **定期检查**: 定期检查EA运行状态和交易结果
5. **参数调整**: 根据市场变化适时调整ATR参数

## 故障排除

### 常见问题
1. **EA无法加载**: 检查文件路径和编译错误
2. **UI不显示**: 确认图表允许EA运行
3. **无法下单**: 检查账户余额和市场状态
4. **计算异常**: 验证ATR指标是否正常工作

### 日志查看
在MT5的"专家"标签页中查看详细的运行日志和错误信息。

## 版本信息

- **版本**: 1.00
- **开发语言**: MQL5
- **兼容平台**: MetaTrader 5
- **最后更新**: 2025年1月

## 技术支持

如有问题或建议，请查看EA运行日志并联系技术支持。

---

**免责声明**: 本EA仅供学习和研究使用，交易有风险，投资需谨慎。使用者应充分了解外汇交易的风险，并根据自身情况谨慎使用。