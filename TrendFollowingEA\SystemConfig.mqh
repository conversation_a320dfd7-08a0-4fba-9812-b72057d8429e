//+------------------------------------------------------------------+
//|                                              SystemConfig.mqh |
//|                                  Copyright 2024, TradingSystem |
//|                                             系统配置文件        |
//+------------------------------------------------------------------+

#ifndef SYSTEM_CONFIG_H
#define SYSTEM_CONFIG_H

//+------------------------------------------------------------------+
//| 系统全局配置类                                                   |
//+------------------------------------------------------------------+
class CSystemConfig
{
public:
    // 市场状态识别配置
    struct SMarketStateConfig
    {
        int atrPeriod;                  // ATR周期
        int atrLongPeriod;              // ATR长期周期
        double atrMultiplier;           // ATR倍数阈值
        int adxPeriod;                  // ADX周期
        double adxThreshold;            // ADX趋势阈值
        int guppyShortStart;            // Guppy短期均线起始
        int guppyShortEnd;              // Guppy短期均线结束
        int guppyLongStart;             // Guppy长期均线起始
        int guppyLongEnd;               // Guppy长期均线结束
    };
    
    // 入场信号配置
    struct SEntryConfig
    {
        int donchianPeriod;             // 唐奇安通道周期
        int breakoutEMAPeriod;          // 突破均线周期
        double breakoutATRMultiplier;   // 突破ATR倍数
    };
    
    // 出场管理配置
    struct SExitConfig
    {
        double initialStopATRMultiplier;// 初始止损ATR倍数
        double trailingStopATRMultiplier;// 跟踪止损ATR倍数
        int chandelierPeriod;           // 吊灯止损周期
    };
    
    // 资金管理配置
    struct SMoneyManagementConfig
    {
        double riskPercentage;          // 单笔风险百分比
        double maxDrawdownPercent;      // 最大回撤百分比
        double reducedRiskPercent;      // 回撤后降低风险百分比
        double pyramidATRMultiplier;    // 加仓ATR倍数
        int maxPyramidLevels;           // 最大加仓层数
    };
    
    // 交易配置
    struct STradeConfig
    {
        int magicNumber;                // 魔术数字
        string tradeComment;            // 交易备注
        bool enablePyramiding;          // 是否启用加仓
        bool enableTrailingStop;        // 是否启用跟踪止损
    };
    
    // 默认配置
    static SMarketStateConfig GetDefaultMarketStateConfig()
    {
        SMarketStateConfig config;
        config.atrPeriod = 14;
        config.atrLongPeriod = 60;
        config.atrMultiplier = 1.5;
        config.adxPeriod = 14;
        config.adxThreshold = 25.0;
        config.guppyShortStart = 3;
        config.guppyShortEnd = 15;
        config.guppyLongStart = 30;
        config.guppyLongEnd = 60;
        return config;
    }
    
    static SEntryConfig GetDefaultEntryConfig()
    {
        SEntryConfig config;
        config.donchianPeriod = 20;
        config.breakoutEMAPeriod = 20;
        config.breakoutATRMultiplier = 2.0;
        return config;
    }
    
    static SExitConfig GetDefaultExitConfig()
    {
        SExitConfig config;
        config.initialStopATRMultiplier = 2.5;
        config.trailingStopATRMultiplier = 2.0;
        config.chandelierPeriod = 22;
        return config;
    }
    
    static SMoneyManagementConfig GetDefaultMoneyManagementConfig()
    {
        SMoneyManagementConfig config;
        config.riskPercentage = 1.0;
        config.maxDrawdownPercent = 15.0;
        config.reducedRiskPercent = 0.5;
        config.pyramidATRMultiplier = 1.5;
        config.maxPyramidLevels = 3;
        return config;
    }
    
    static STradeConfig GetDefaultTradeConfig()
    {
        STradeConfig config;
        config.magicNumber = 888888;
        config.tradeComment = "TrendFollowing";
        config.enablePyramiding = true;
        config.enableTrailingStop = true;
        return config;
    }
};

//+------------------------------------------------------------------+
//| 系统常量定义                                                     |
//+------------------------------------------------------------------+
class CSystemConstants
{
public:
    // 时间常量
    static const int SECONDS_IN_MINUTE = 60;
    static const int MINUTES_IN_HOUR = 60;
    static const int HOURS_IN_DAY = 24;
    static const int DAYS_IN_WEEK = 7;
    
    // 价格精度
    static const int PRICE_PRECISION = 5;
    
    // 最小/最大值
    static const double MIN_ATR_MULTIPLIER = 0.5;
    static const double MAX_ATR_MULTIPLIER = 10.0;
    static const double MIN_RISK_PERCENT = 0.1;
    static const double MAX_RISK_PERCENT = 10.0;
    static const int MIN_PERIOD = 1;
    static const int MAX_PERIOD = 200;
    
    // 系统状态
    static const string SYSTEM_NAME = "趋势跟踪交易系统";
    static const string SYSTEM_VERSION = "1.0.0";
    static const string SYSTEM_AUTHOR = "TradingSystem";
    
    // 日志级别
    enum ENUM_LOG_LEVEL
    {
        LOG_LEVEL_ERROR = 0,    // 错误
        LOG_LEVEL_WARNING = 1,  // 警告
        LOG_LEVEL_INFO = 2,     // 信息
        LOG_LEVEL_DEBUG = 3     // 调试
    };
};

//+------------------------------------------------------------------+
//| 系统工具类                                                       |
//+------------------------------------------------------------------+
class CSystemUtils
{
public:
    // 日志记录
    static void Log(CSystemConstants::ENUM_LOG_LEVEL level, string message)
    {
        string prefix = "";
        switch(level)
        {
            case CSystemConstants::LOG_LEVEL_ERROR:   prefix = "[ERROR] "; break;
            case CSystemConstants::LOG_LEVEL_WARNING: prefix = "[WARNING] "; break;
            case CSystemConstants::LOG_LEVEL_INFO:    prefix = "[INFO] "; break;
            case CSystemConstants::LOG_LEVEL_DEBUG:   prefix = "[DEBUG] "; break;
        }
        
        Print(prefix + TimeToString(TimeCurrent(), TIME_DATE|TIME_SECONDS) + " - " + message);
    }
    
    // 参数验证
    static bool ValidateATRMultiplier(double value)
    {
        return (value >= CSystemConstants::MIN_ATR_MULTIPLIER && 
                value <= CSystemConstants::MAX_ATR_MULTIPLIER);
    }
    
    static bool ValidateRiskPercent(double value)
    {
        return (value >= CSystemConstants::MIN_RISK_PERCENT && 
                value <= CSystemConstants::MAX_RISK_PERCENT);
    }
    
    static bool ValidatePeriod(int value)
    {
        return (value >= CSystemConstants::MIN_PERIOD && 
                value <= CSystemConstants::MAX_PERIOD);
    }
    
    // 价格格式化
    static string FormatPrice(double price)
    {
        return DoubleToString(price, CSystemConstants::PRICE_PRECISION);
    }
    
    // 百分比格式化
    static string FormatPercent(double percent)
    {
        return DoubleToString(percent, 2) + "%";
    }
    
    // 检查交易时间
    static bool IsTradingTime()
    {
        MqlDateTime dt;
        TimeToStruct(TimeCurrent(), dt);
        
        // 避免周末交易
        if(dt.day_of_week == 0 || dt.day_of_week == 6)
            return false;
        
        // 避免重要新闻时间（可根据需要调整）
        // 这里可以添加更复杂的时间过滤逻辑
        
        return true;
    }
    
    // 计算两个价格之间的点数差
    static double CalculatePointsDifference(double price1, double price2)
    {
        double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
        return MathAbs(price1 - price2) / point;
    }
    
    // 获取当前点差
    static double GetCurrentSpread()
    {
        double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
        double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
        double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
        return (ask - bid) / point;
    }
    
    // 检查点差是否合理
    static bool IsSpreadAcceptable(double maxSpreadPoints = 30.0)
    {
        return (GetCurrentSpread() <= maxSpreadPoints);
    }
};

#endif // SYSTEM_CONFIG_H