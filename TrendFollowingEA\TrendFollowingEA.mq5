//+------------------------------------------------------------------+
//|                                           TrendFollowingEA.mq5 |
//|                                  Copyright 2024, TradingSystem |
//|                                             趋势跟踪交易系统    |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, TradingSystem"
#property link      ""
#property version   "1.00"
#property description "全自动趋势跟踪交易系统 - 四大核心模块架构"

// 包含所有模块
#include "MarketStateAnalyzer.mqh"
#include "EntrySignalModule.mqh"
#include "ExitManager.mqh"
#include "PositionManager.mqh"

//--- 输入参数
input group "=== 市场状态识别开关 ==="
input bool InpEnableATR = true;                 // 启用ATR波动率识别
input bool InpEnableADX = true;                 // 启用ADX趋势强度识别
input bool InpEnableGMMA = true;                // 启用GMMA趋势识别

input group "=== 市场状态识别参数 ==="
input int InpATRPeriod = 14;                    // ATR周期
input int InpATRLongPeriod = 60;                // ATR长期周期
input double InpATRMultiplier = 1.5;            // ATR倍数阈值
input int InpADXPeriod = 14;                    // ADX周期
input double InpADXThreshold = 25.0;            // ADX趋势阈值
input int InpGuppy_Short_Start = 3;             // Guppy短期均线起始周期
input int InpGuppy_Short_End = 15;              // Guppy短期均线结束周期
input int InpGuppy_Long_Start = 30;             // Guppy长期均线起始周期
input int InpGuppy_Long_End = 60;               // Guppy长期均线结束周期

input group "=== 入场信号开关 ==="
input bool InpEnableDonchianBreakout = true;    // 启用唐奇安通道突破信号
input bool InpEnableEMABreakout = true;         // 启用均线突破信号
input bool InpEnableATRFilter = true;           // 启用ATR过滤器

input group "=== 入场信号参数 ==="
input int InpDonchianPeriod = 20;               // 唐奇安通道周期
input int InpBreakoutEMAPeriod = 20;            // 突破均线周期
input double InpBreakoutATRMultiplier = 2.0;    // 突破ATR倍数

input group "=== 出场管理参数 ==="
input double InpInitialStopATRMultiplier = 2.5; // 初始止损ATR倍数
input double InpTrailingStopATRMultiplier = 2.0;// 跟踪止损ATR倍数
input int InpChandelierPeriod = 22;             // 吊灯止损周期

input group "=== 资金管理参数 ==="
input double InpRiskPercentage = 1.0;           // 单笔风险百分比
input double InpMaxDrawdownPercent = 15.0;      // 最大回撤百分比
input double InpReducedRiskPercent = 0.5;       // 回撤后降低风险百分比
input double InpPyramidATRMultiplier = 1.5;     // 加仓ATR倍数
input int InpMaxPyramidLevels = 3;              // 最大加仓层数
input int InpMaxPositionsPerSymbol = 3;         // 单一品种最大持仓笔数

input group "=== 其他参数 ==="
input int InpMagicNumber = 888888;              // 魔术数字
input string InpTradeComment = "TrendFollowing";// 交易备注

//--- 全局变量
CMarketStateAnalyzer* g_MarketAnalyzer;
CEntrySignalModule* g_EntryModule;
CExitManager* g_ExitManager;
CPositionManager* g_PositionManager;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("=== 趋势跟踪EA初始化开始 ===");
    
    // 创建各个模块实例
    g_MarketAnalyzer = new CMarketStateAnalyzer();
    g_EntryModule = new CEntrySignalModule();
    g_ExitManager = new CExitManager();
    g_PositionManager = new CPositionManager();
    
    // 初始化市场状态分析器
    if(!g_MarketAnalyzer.Init(InpATRPeriod, InpATRLongPeriod, InpATRMultiplier,
                              InpADXPeriod, InpADXThreshold,
                              InpGuppy_Short_Start, InpGuppy_Short_End,
                              InpGuppy_Long_Start, InpGuppy_Long_End,
                              InpEnableATR, InpEnableADX, InpEnableGMMA))
    {
        Print("市场状态分析器初始化失败");
        return INIT_FAILED;
    }
    
    // 初始化入场信号模块
    if(!g_EntryModule.Init(InpDonchianPeriod, InpBreakoutEMAPeriod, 
                           InpBreakoutATRMultiplier, InpATRPeriod,
                           InpEnableDonchianBreakout, InpEnableEMABreakout, InpEnableATRFilter))
    {
        Print("入场信号模块初始化失败");
        return INIT_FAILED;
    }
    
    // 初始化出场管理器
    if(!g_ExitManager.Init(InpInitialStopATRMultiplier, InpTrailingStopATRMultiplier,
                           InpChandelierPeriod, InpATRPeriod))
    {
        Print("出场管理器初始化失败");
        return INIT_FAILED;
    }
    
    // 初始化仓位管理器
    if(!g_PositionManager.Init(InpRiskPercentage, InpMaxDrawdownPercent,
                               InpReducedRiskPercent, InpPyramidATRMultiplier,
                               InpMaxPyramidLevels, InpMaxPositionsPerSymbol, 
                               InpMagicNumber, InpTradeComment))
    {
        Print("仓位管理器初始化失败");
        return INIT_FAILED;
    }
    
    Print("=== 趋势跟踪EA初始化完成 ===");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("=== 趋势跟踪EA反初始化 ===");
    
    // 清理资源
    if(g_MarketAnalyzer != NULL)
    {
        delete g_MarketAnalyzer;
        g_MarketAnalyzer = NULL;
    }
    
    if(g_EntryModule != NULL)
    {
        delete g_EntryModule;
        g_EntryModule = NULL;
    }
    
    if(g_ExitManager != NULL)
    {
        delete g_ExitManager;
        g_ExitManager = NULL;
    }
    
    if(g_PositionManager != NULL)
    {
        delete g_PositionManager;
        g_PositionManager = NULL;
    }
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // 检查是否为新K线
    static datetime lastBarTime = 0;
    datetime currentBarTime = iTime(_Symbol, PERIOD_CURRENT, 0);
    
    if(currentBarTime == lastBarTime)
        return;
    
    lastBarTime = currentBarTime;
    
    // 更新所有模块
    g_MarketAnalyzer.Update();
    g_EntryModule.Update();
    g_ExitManager.Update();
    g_PositionManager.Update();
    
    // 1. 首先处理出场逻辑（优先级最高）
    g_ExitManager.ProcessExits();
    
    // 2. 检查市场状态
    ENUM_MARKET_STATE marketState = g_MarketAnalyzer.GetMarketState();
    
    if(marketState == MARKET_STATE_RANGING)
    {
        // 震荡市，不开新仓
        return;
    }
    
    // 3. 处理入场信号
    ENUM_SIGNAL_TYPE entrySignal = g_EntryModule.GetEntrySignal();
    
    if(entrySignal != SIGNAL_NONE)
    {
        // 有入场信号，交给仓位管理器处理
        g_PositionManager.ProcessEntry(entrySignal, marketState);
    }
    
    // 4. 处理加仓逻辑
    g_PositionManager.ProcessPyramiding();
}

//+------------------------------------------------------------------+
//| Trade function                                                   |
//+------------------------------------------------------------------+
void OnTrade()
{
    // 更新仓位管理器的交易状态
    if(g_PositionManager != NULL)
        g_PositionManager.OnTradeEvent();
}

//+------------------------------------------------------------------+
//| Timer function                                                   |
//+------------------------------------------------------------------+
void OnTimer()
{
    // 定期检查系统状态
    if(g_PositionManager != NULL)
        g_PositionManager.CheckSystemHealth();
}