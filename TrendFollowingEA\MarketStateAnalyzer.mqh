//+------------------------------------------------------------------+
//|                                        MarketStateAnalyzer.mqh |
//|                                  Copyright 2024, TradingSystem |
//|                                           市场状态识别模块      |
//+------------------------------------------------------------------+

#ifndef MARKET_STATE_ANALYZER_H
#define MARKET_STATE_ANALYZER_H

//--- 市场状态枚举
enum ENUM_MARKET_STATE
{
    MARKET_STATE_UNKNOWN,      // 未知状态
    MARKET_STATE_TRENDING_UP,  // 上升趋势
    MARKET_STATE_TRENDING_DOWN,// 下降趋势
    MARKET_STATE_RANGING       // 震荡市
};

//+------------------------------------------------------------------+
//| 市场状态分析器类                                                 |
//+------------------------------------------------------------------+
class CMarketStateAnalyzer
{
private:
    // ATR相关
    int m_atrHandle;
    int m_atrLongHandle;
    int m_atrPeriod;
    int m_atrLongPeriod;
    double m_atrMultiplier;
    bool m_enableATR;
    
    // ADX相关
    int m_adxHandle;
    int m_adxPeriod;
    double m_adxThreshold;
    bool m_enableADX;
    
    // Guppy均线组
    int m_guppyShortHandles[];
    int m_guppyLongHandles[];
    int m_guppyShortStart;
    int m_guppyShortEnd;
    int m_guppyLongStart;
    int m_guppyLongEnd;
    bool m_enableGMMA;
    
    // 当前市场状态
    ENUM_MARKET_STATE m_currentState;
    
    // 私有方法
    bool CheckATRVolatility();
    bool CheckADXTrend();
    ENUM_MARKET_STATE AnalyzeGuppyTrend();
    
public:
    CMarketStateAnalyzer();
    ~CMarketStateAnalyzer();
    
    bool Init(int atrPeriod, int atrLongPeriod, double atrMultiplier,
              int adxPeriod, double adxThreshold,
              int guppyShortStart, int guppyShortEnd,
              int guppyLongStart, int guppyLongEnd,
              bool enableATR, bool enableADX, bool enableGMMA);
    
    void Update();
    ENUM_MARKET_STATE GetMarketState() const { return m_currentState; }
    
    // 获取技术指标值
    double GetCurrentATR();
    double GetCurrentADX();
    bool IsVolatilityExpanding();
};

//+------------------------------------------------------------------+
//| 构造函数                                                         |
//+------------------------------------------------------------------+
CMarketStateAnalyzer::CMarketStateAnalyzer()
{
    m_atrHandle = INVALID_HANDLE;
    m_atrLongHandle = INVALID_HANDLE;
    m_adxHandle = INVALID_HANDLE;
    m_currentState = MARKET_STATE_UNKNOWN;
    
    ArrayResize(m_guppyShortHandles, 0);
    ArrayResize(m_guppyLongHandles, 0);
}

//+------------------------------------------------------------------+
//| 析构函数                                                         |
//+------------------------------------------------------------------+
CMarketStateAnalyzer::~CMarketStateAnalyzer()
{
    if(m_atrHandle != INVALID_HANDLE)
        IndicatorRelease(m_atrHandle);
    if(m_atrLongHandle != INVALID_HANDLE)
        IndicatorRelease(m_atrLongHandle);
    if(m_adxHandle != INVALID_HANDLE)
        IndicatorRelease(m_adxHandle);
    
    // 释放Guppy均线句柄
    for(int i = 0; i < ArraySize(m_guppyShortHandles); i++)
    {
        if(m_guppyShortHandles[i] != INVALID_HANDLE)
            IndicatorRelease(m_guppyShortHandles[i]);
    }
    
    for(int i = 0; i < ArraySize(m_guppyLongHandles); i++)
    {
        if(m_guppyLongHandles[i] != INVALID_HANDLE)
            IndicatorRelease(m_guppyLongHandles[i]);
    }
}

//+------------------------------------------------------------------+
//| 初始化函数                                                       |
//+------------------------------------------------------------------+
bool CMarketStateAnalyzer::Init(int atrPeriod, int atrLongPeriod, double atrMultiplier,
                                int adxPeriod, double adxThreshold,
                                int guppyShortStart, int guppyShortEnd,
                                int guppyLongStart, int guppyLongEnd,
                                bool enableATR, bool enableADX, bool enableGMMA)
{
    // 保存参数
    m_atrPeriod = atrPeriod;
    m_atrLongPeriod = atrLongPeriod;
    m_atrMultiplier = atrMultiplier;
    m_adxPeriod = adxPeriod;
    m_adxThreshold = adxThreshold;
    m_guppyShortStart = guppyShortStart;
    m_guppyShortEnd = guppyShortEnd;
    m_guppyLongStart = guppyLongStart;
    m_guppyLongEnd = guppyLongEnd;
    m_enableATR = enableATR;
    m_enableADX = enableADX;
    m_enableGMMA = enableGMMA;
    
    // 创建ATR指标（如果启用）
    if(m_enableATR)
    {
        m_atrHandle = iATR(_Symbol, PERIOD_CURRENT, m_atrPeriod);
        m_atrLongHandle = iATR(_Symbol, PERIOD_CURRENT, m_atrLongPeriod);
        
        if(m_atrHandle == INVALID_HANDLE || m_atrLongHandle == INVALID_HANDLE)
        {
            Print("ATR指标创建失败");
            return false;
        }
    }
    
    // 创建ADX指标（如果启用）
    if(m_enableADX)
    {
        m_adxHandle = iADX(_Symbol, PERIOD_CURRENT, m_adxPeriod);
        if(m_adxHandle == INVALID_HANDLE)
        {
            Print("ADX指标创建失败");
            return false;
        }
    }
    
    // 创建Guppy均线组（如果启用）
    if(m_enableGMMA)
    {
        // 创建Guppy短期均线组
        int shortCount = (m_guppyShortEnd - m_guppyShortStart) / 2 + 1;
        ArrayResize(m_guppyShortHandles, shortCount);
        
        int index = 0;
        for(int period = m_guppyShortStart; period <= m_guppyShortEnd; period += 2)
        {
            m_guppyShortHandles[index] = iMA(_Symbol, PERIOD_CURRENT, period, 0, MODE_EMA, PRICE_CLOSE);
            if(m_guppyShortHandles[index] == INVALID_HANDLE)
            {
                Print("Guppy短期均线创建失败，周期：", period);
                return false;
            }
            index++;
        }
        
        // 创建Guppy长期均线组
        int longCount = (m_guppyLongEnd - m_guppyLongStart) / 5 + 1;
        ArrayResize(m_guppyLongHandles, longCount);
        
        index = 0;
        for(int period = m_guppyLongStart; period <= m_guppyLongEnd; period += 5)
        {
            m_guppyLongHandles[index] = iMA(_Symbol, PERIOD_CURRENT, period, 0, MODE_EMA, PRICE_CLOSE);
            if(m_guppyLongHandles[index] == INVALID_HANDLE)
            {
                Print("Guppy长期均线创建失败，周期：", period);
                return false;
            }
            index++;
        }
    }
    
    Print("市场状态分析器初始化成功 - ATR:", (m_enableATR ? "启用" : "禁用"), 
          " ADX:", (m_enableADX ? "启用" : "禁用"), 
          " GMMA:", (m_enableGMMA ? "启用" : "禁用"));
    return true;
}

//+------------------------------------------------------------------+
//| 更新函数                                                         |
//+------------------------------------------------------------------+
void CMarketStateAnalyzer::Update()
{
    // 检查波动率扩张（如果启用ATR）
    bool volatilityExpanding = m_enableATR ? CheckATRVolatility() : true;
    
    // 检查ADX趋势强度（如果启用ADX）
    bool adxTrending = m_enableADX ? CheckADXTrend() : true;
    
    // 分析Guppy趋势（如果启用GMMA）
    ENUM_MARKET_STATE guppyState = m_enableGMMA ? AnalyzeGuppyTrend() : MARKET_STATE_TRENDING_UP;
    
    // 综合判断市场状态
    // 如果所有启用的指标都确认趋势，则认为是趋势市场
    bool isTrending = volatilityExpanding && adxTrending;
    
    if(m_enableGMMA)
    {
        // 如果启用GMMA，需要GMMA也确认趋势
        if(isTrending && guppyState != MARKET_STATE_RANGING)
        {
            m_currentState = guppyState;
        }
        else
        {
            m_currentState = MARKET_STATE_RANGING;
        }
    }
    else
    {
        // 如果未启用GMMA，根据价格行为简单判断趋势方向
        if(isTrending)
        {
            double close1 = iClose(_Symbol, PERIOD_CURRENT, 1);
            double close10 = iClose(_Symbol, PERIOD_CURRENT, 10);
            m_currentState = (close1 > close10) ? MARKET_STATE_TRENDING_UP : MARKET_STATE_TRENDING_DOWN;
        }
        else
        {
            m_currentState = MARKET_STATE_RANGING;
        }
    }
}

//+------------------------------------------------------------------+
//| 检查ATR波动率                                                    |
//+------------------------------------------------------------------+
bool CMarketStateAnalyzer::CheckATRVolatility()
{
    if(!m_enableATR)
        return true; // 如果未启用ATR，默认返回true
        
    double atrCurrent[], atrLong[];
    
    if(CopyBuffer(m_atrHandle, 0, 1, 1, atrCurrent) <= 0 ||
       CopyBuffer(m_atrLongHandle, 0, 1, 1, atrLong) <= 0)
    {
        return false;
    }
    
    // ATR(14) > ATR(60) * 1.5
    return (atrCurrent[0] > atrLong[0] * m_atrMultiplier);
}

//+------------------------------------------------------------------+
//| 检查ADX趋势强度                                                  |
//+------------------------------------------------------------------+
bool CMarketStateAnalyzer::CheckADXTrend()
{
    if(!m_enableADX)
        return true; // 如果未启用ADX，默认返回true
        
    double adxValues[];
    
    if(CopyBuffer(m_adxHandle, 0, 1, 1, adxValues) <= 0)
        return false;
    
    return (adxValues[0] > m_adxThreshold);
}

//+------------------------------------------------------------------+
//| 分析Guppy趋势                                                    |
//+------------------------------------------------------------------+
ENUM_MARKET_STATE CMarketStateAnalyzer::AnalyzeGuppyTrend()
{
    double shortMAs[], longMAs[];
    int shortSize = ArraySize(m_guppyShortHandles);
    int longSize = ArraySize(m_guppyLongHandles);
    
    ArrayResize(shortMAs, shortSize);
    ArrayResize(longMAs, longSize);
    
    // 获取短期均线组数值
    for(int i = 0; i < shortSize; i++)
    {
        double buffer[];
        if(CopyBuffer(m_guppyShortHandles[i], 0, 1, 1, buffer) <= 0)
            return MARKET_STATE_RANGING;
        shortMAs[i] = buffer[0];
    }
    
    // 获取长期均线组数值
    for(int i = 0; i < longSize; i++)
    {
        double buffer[];
        if(CopyBuffer(m_guppyLongHandles[i], 0, 1, 1, buffer) <= 0)
            return MARKET_STATE_RANGING;
        longMAs[i] = buffer[0];
    }
    
    // 检查短期均线组是否在长期均线组之上（上升趋势）
    bool allShortAboveLong = true;
    bool allShortBelowLong = true;
    
    for(int i = 0; i < shortSize; i++)
    {
        for(int j = 0; j < longSize; j++)
        {
            if(shortMAs[i] <= longMAs[j])
                allShortAboveLong = false;
            if(shortMAs[i] >= longMAs[j])
                allShortBelowLong = false;
        }
    }
    
    // 检查均线组内部是否发散
    bool shortDiverging = true;
    bool longDiverging = true;
    
    // 检查短期均线组发散（递增排列为上升发散）
    for(int i = 1; i < shortSize; i++)
    {
        if(shortMAs[i] <= shortMAs[i-1])
        {
            shortDiverging = false;
            break;
        }
    }
    
    // 检查长期均线组发散
    for(int i = 1; i < longSize; i++)
    {
        if(longMAs[i] <= longMAs[i-1])
        {
            longDiverging = false;
            break;
        }
    }
    
    // 判断趋势状态
    if(allShortAboveLong && shortDiverging && longDiverging)
        return MARKET_STATE_TRENDING_UP;
    else if(allShortBelowLong && shortDiverging && longDiverging)
        return MARKET_STATE_TRENDING_DOWN;
    else
        return MARKET_STATE_RANGING;
}

//+------------------------------------------------------------------+
//| 获取当前ATR值                                                    |
//+------------------------------------------------------------------+
double CMarketStateAnalyzer::GetCurrentATR()
{
    double atrValues[];
    if(CopyBuffer(m_atrHandle, 0, 1, 1, atrValues) <= 0)
        return 0.0;
    return atrValues[0];
}

//+------------------------------------------------------------------+
//| 获取当前ADX值                                                    |
//+------------------------------------------------------------------+
double CMarketStateAnalyzer::GetCurrentADX()
{
    double adxValues[];
    if(CopyBuffer(m_adxHandle, 0, 1, 1, adxValues) <= 0)
        return 0.0;
    return adxValues[0];
}

//+------------------------------------------------------------------+
//| 检查波动率是否扩张                                               |
//+------------------------------------------------------------------+
bool CMarketStateAnalyzer::IsVolatilityExpanding()
{
    return CheckATRVolatility();
}

#endif // MARKET_STATE_ANALYZER_H